@import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap');

:root {
  --primary-color: #017fff;
  --ck-color-base-border: #eeeef0 !important;
  --ck-focus-ring: 1px solid #017fff !important;
  --ck-border-radius: 4px !important;
}

body {
  font-family: 'Nunito Sans', sans-serif !important;
  line-height: 24px;
  overflow: hidden;
}

a {
  text-decoration: none;
  color: #2187ff;
}

button {
  padding: 0;
  outline: none;
  border: none;
  background-color: transparent;
  cursor: pointer;
  &[disabled] {
    opacity: 0.8;
    cursor: not-allowed;
  }
}

pre {
  white-space: pre-wrap;
  word-break: break-all;
}

.icon {
  width: 24px;
  height: 24px;
  // cursor: pointer;
  &-lg {
    width: 32px;
    height: 32px;
    // cursor: pointer;
  }
  &-xl {
    width: 48px;
    height: 48px;
  }
}

.p-error {
  font-size: 12px;
  line-height: 18px;
}

.name-page {
  font-size: 24px;
  font-weight: 700;
  line-height: 30px;
  color: #313131;
}

.box {
  padding: 24px;
  background-color: #ffffff;
  border-radius: 4px;
}

::-webkit-scrollbar {
  width: 7px !important;
  border-radius: 10px !important;
  height: 7px;
}

/* Track */
::-webkit-scrollbar-track {
  background: none !important;
  border-radius: 10px !important;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #ccc !important;
  border-radius: 10px !important;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #ccc !important;
  border-radius: 10px !important;
}

// Table
.p-datatable .p-datatable-thead > tr > th {
  background-color: #ffffff;
}
.p-datatable .p-datatable-thead > tr > th,
.p-datatable .p-datatable-tbody > tr > td,
.p-datatable .p-datatable-footer {
  border-color: #efefef;
}

// Paginator
.p-paginator {
  justify-content: flex-end !important;
}
.p-paginator .p-paginator-pages .p-paginator-page,
.p-paginator .p-paginator-first,
.p-paginator .p-paginator-prev,
.p-paginator .p-paginator-next,
.p-paginator .p-paginator-last {
  padding: 4px 12px;
  margin: 0 4px;
  min-width: 32px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 4px;
}
.p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
  background-color: #017fff;
  color: #ffffff;
  border-color: transparent;
}

// Button
.p-button.p-button-sm {
  padding: 8px 16px;
  gap: 8px;
}

.p-button.p-button-cancel,
.p-buttonset.p-button-cancel > .p-button,
.p-splitbutton.p-button-cancel > .p-button {
  color: #8a8a8a;
  background: #efefef;
  border: 1px solid #efefef;
}
.p-button.p-button-cancel:enabled:hover,
.p-buttonset.p-button-cancel > .p-button:enabled:hover,
.p-splitbutton.p-button-cancel > .p-button:enabled:hover {
  background: #d7d7d7;
  color: #8a8a8a;
  border-color: #d7d7d7;
}
.p-button.p-button-cancel:enabled:focus,
.p-buttonset.p-button-cancel > .p-button:enabled:focus,
.p-splitbutton.p-button-cancel > .p-button:enabled:focus {
  box-shadow: 0 0 0 0.2rem #e2e8f0;
}
.p-button.p-button-cancel:enabled:active,
.p-buttonset.p-button-cancel > .p-button:enabled:active,
.p-splitbutton.p-button-cancel > .p-button:enabled:active {
  background: #505d6f;
  color: #fff;
  border-color: #505d6f;
}

// Overlay Panel
.p-overlaypanel {
  box-shadow: 0px 1px 12px 0px rgba(0, 0, 0, 0.08);
  border-radius: 4px;
}

// Ckeditor
.ck-placeholder {
  &::before {
    font-weight: 400;
    color: #a3a3a6 !important;
  }
}
.ck-powered-by-balloon {
  display: none !important;
}

.ck-content {
  h1,
  h2,
  h3,
  h4,
  p,
  b,
  i,
  s,
  u,
  ul,
  li,
  ol,
  a {
    all: revert;
  }
}

// Dialog
.p-dialog .p-dialog-content:last-of-type {
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
}

.p-dialog.p-component.p-confirm-dialog {
  min-width: 450px;
}

// Dialog Confirm
.p-dialog .p-dialog-footer .p-confirm-dialog-accept {
  margin-right: 0;
}

// Reset style
.reset-style {
  * {
    all: revert;
  }
}
.content_html {
  img {
    max-width: 100% !important;
    overflow: hidden;
    margin: auto !important;
    display: block !important;
  }
  figure.image.image_resized {
    margin: auto !important;
    img {
      width: 100% !important;
    }
  }
}
.pricing-card > * {
  ::-webkit-scrollbar {
    display: none;
  }
}
// input#quantity {
//   width: 100%;
// }
// .page-enter-active,
// .page-leave-active {
//   transition: all 0.3s;
// }
// .page-enter-from,
// .page-leave-to {
//   opacity: 0;
//   filter: blur(1rem);
// }

textarea {
  &::placeholder {
    font-family: 'Nunito Sans', sans-serif !important;
  }
  font-family: 'Nunito Sans', sans-serif !important;
}
