<svg
						id="vuesax_linear_card"
						data-name="vuesax/linear/card"
						xmlns="http://www.w3.org/2000/svg"
						width="24"
						height="24"
						viewBox="0 0 24 24"
					>
						<g id="card">
							<path
								id="Vector"
								d="M0,0H20"
								transform="translate(2 8.505)"
								fill="none"
								stroke="#fff"
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="1.5"
							/>
							<path
								id="Vector-2"
								data-name="Vector"
								d="M0,0H2"
								transform="translate(6 16.505)"
								fill="none"
								stroke="#fff"
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="1.5"
							/>
							<path
								id="Vector-3"
								data-name="Vector"
								d="M0,0H4"
								transform="translate(10.5 16.505)"
								fill="none"
								stroke="#fff"
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="1.5"
							/>
							<path
								id="Vector-4"
								data-name="Vector"
								d="M4.44,0H15.55C19.11,0,20,.88,20,4.39V12.6c0,3.51-.89,4.39-4.44,4.39H4.44C.89,17,0,16.12,0,12.61V4.39C0,.88.89,0,4.44,0Z"
								transform="translate(2 3.505)"
								fill="none"
								stroke="#fff"
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="1.5"
							/>
							<path id="Vector-5" data-name="Vector" d="M0,0H24V24H0Z" fill="none" opacity="0" />
						</g>
					</svg>
