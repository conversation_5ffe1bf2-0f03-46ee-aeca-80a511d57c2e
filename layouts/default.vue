<script setup lang="ts">
import { useToast } from 'primevue/usetoast'
import { useAppStore } from '~/stores/app'
import TheSidebarLeft from 'components/TheSidebarLeft.vue'
import TheHeader from 'components/TheHeader.vue'
import LoadingBar from 'components/LoadingBar.vue'

const appStore = useAppStore()
const toast = useToast()

watch(
  () => appStore.error,
  (error) => {
    if (error) {
      toast.add({ severity: 'error', summary: 'Error Message ' + error?.code, detail: error?.message, life: 3000 })
      appStore.error = null
    }
  },
  { deep: true },
)
</script>

<template>
  <div class="layout-default flex">
    <TheSidebarLeft />
    <LoadingBar />
    <div class="h-screen fc flex-1">
      <TheHeader />
      <div class="flex flex-1 overflow-auto bg-[#F7F8FA]">
        <NuxtPage class="page w-full" />
      </div>
    </div>
    <ConfirmDialog></ConfirmDialog>
    <Toast />
  </div>
</template>
