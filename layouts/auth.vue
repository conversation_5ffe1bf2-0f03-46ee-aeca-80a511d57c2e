<script setup lang="ts">
import AuthLeft from 'components/auth/AuthLeft.vue'
import { useToast } from 'primevue/usetoast'
import { useAppStore } from '~/stores/app'

const appStore = useAppStore()
const toast = useToast()

watch(
  () => appStore.error,
  (error) => {
    if (error) {
      toast.add({ severity: 'error', summary: 'Error Message ' + error?.code, detail: error?.message, life: 3000 })
      appStore.error = null
    }
  },
  { deep: true },
)
</script>

<template>
  <div class="layout-auth h-screen overflow-auto">
    <div class="flex">
      <AuthLeft class="flex-1 min-h-screen order-1" />
      <NuxtPage class="w-[40%] min-h-screen" />
    </div>
  </div>
  <Toast />
</template>
