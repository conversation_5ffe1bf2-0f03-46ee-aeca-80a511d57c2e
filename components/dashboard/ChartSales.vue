<script lang="ts" setup>
import ChartSalesRevenue from './ChartSalesRevenue.vue'
import ChartSalesVolume from './ChartSalesVolume.vue'
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
})
const chartType = ref('volume')
const listChartType = [
  {
    value: 'volume',
    label: 'Khối lượng',
  },
  {
    value: 'revenue',
    label: 'Doanh số',
  },
]
</script>

<template>
  <div class="page w-full">
    <div class="com-heading fr jc-sb ai-c mb-4">
      Tỉ lệ mua hàng
      <div class="fr gap-2">
        <div
          class="text-base font-semibold c-black rounded-16px cursor-pointer px-3 py-1 transition-all"
          :class="{ '!c-white bg-primary': item.value === chartType }"
          @click="chartType = item.value"
          v-for="item in listChartType">
          {{ item.label }}
        </div>
      </div>
    </div>
    <ChartSalesRevenue :data="data" v-if="chartType === 'revenue'" />
    <ChartSalesVolume :data="data" v-if="chartType === 'volume'" />
  </div>
</template>
