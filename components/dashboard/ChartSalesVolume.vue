<script lang="ts" setup>
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
})
const currentMonth = ref(new Date().getMonth())
const currentYear = ref(new Date().getFullYear())
const currentQuarter = ref(Math.ceil(new Date().getMonth() / 3))
const dataChart = computed(() => {
  return {
    chartOptions: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#F2F7F9',
            strokeWidth: '100%',
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: 0,
        },
      },
      fill: {
        colors: ['#FDBD10'],
      },
    },
    series: props.data.VPerMonth ? [useParseNumber(props.data.VPerMonth).toFixed()] : [0],
  }
})
const dataChartChild = computed(() => {
  return {
    chartOptions: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#F2F7F9',
            strokeWidth: '100%',
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: 0,
        },
      },
      fill: {
        colors: ['#F32246'],
      },
    },
    series:
      props.data.VPerMonth && useParseNumber(props.data.VPerMonth) > 100
        ? [useParseNumber(props.data.VPerMonth) - 100]
        : [99],
  }
})
const dataChartQuater = computed(() => {
  return {
    chartOptions: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#F2F7F9',
            strokeWidth: '100%',
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: 0,
        },
      },
      fill: {
        colors: ['#FDBD10'],
      },
    },
    series: props.data.VPerQuarter ? [useParseNumber(props.data.VPerQuarter).toFixed()] : [0],
  }
})
const dataChartChildQuater = computed(() => {
  return {
    chartOptions: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#F2F7F9',
            strokeWidth: '100%',
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: 0,
        },
      },
      fill: {
        colors: ['#F32246'],
      },
    },
    series:
      props.data.VPerQuarter && useParseNumber(props.data.VPerQuarter) > 100
        ? [useParseNumber(props.data.VPerQuarter) - 100]
        : [99],
  }
})
const dataChartYear = computed(() => {
  return {
    chartOptions: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#F2F7F9',
            strokeWidth: '100%',
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: 0,
        },
      },
      fill: {
        colors: ['#FDBD10'],
      },
    },
    series: props.data.VPerYear ? [useParseNumber(props.data.VPerYear).toFixed()] : [0],
  }
})
const dataChartChildYear = computed(() => {
  return {
    chartOptions: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#F2F7F9',
            strokeWidth: '100%',
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: 0,
        },
      },
      fill: {
        colors: ['#F32246'],
      },
    },
    series:
      props.data.VPerYear && useParseNumber(props.data.VPerYear) > 100
        ? [useParseNumber(props.data.VPerYear) - 100]
        : [99],
  }
})
</script>

<template>
  <div class="w-full grid grid-cols-3">
    <div class="fc ai-c">
      <div class="relative">
        <apexcharts stype="radialBar" :options="dataChart.chartOptions" :series="dataChart.series"></apexcharts>
        <div
          class="w-[70%] absolute left-[50%] translate-x-[-50%] bottom-0"
          v-if="useParseNumber(data.VPerMonth) > 100">
          <apexcharts
            stype="radialBar"
            :options="dataChartChild.chartOptions"
            :series="dataChartChild.series"></apexcharts>
        </div>
        <div class="c-primary text-sm font-bold absolute left-[50%] translate-x-[-50%] bottom-16px">
          Tháng {{ currentMonth }}
        </div>
      </div>
      <div class="fc">
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Doanh thu</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.VolumeMonth) }}</div>
        </div>
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Target</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.VMonthTarget) }}</div>
        </div>
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Đạt</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.VPerMonth) }}%</div>
        </div>
      </div>
    </div>

    <div class="fc ai-c">
      <div class="relative">
        <apexcharts
          stype="radialBar"
          :options="dataChartQuater.chartOptions"
          :series="dataChartQuater.series"></apexcharts>
        <div
          class="w-[70%] absolute left-[50%] translate-x-[-50%] bottom-0"
          v-if="useParseNumber(data.VPerQuarter) > 100">
          <apexcharts
            stype="radialBar"
            :options="dataChartChildQuater.chartOptions"
            :series="dataChartChildQuater.series"></apexcharts>
        </div>
        <div class="c-primary text-sm font-bold absolute left-[50%] translate-x-[-50%] bottom-16px">
          Quý {{ currentQuarter }}
        </div>
      </div>
      <div class="fc">
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Doanh thu</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.VolumeQuarter) }}</div>
        </div>
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Target</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.VQuarterTarget) }}</div>
        </div>
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Đạt</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.VPerQuarter) }}%</div>
        </div>
      </div>
    </div>

    <div class="fc ai-c">
      <div class="relative">
        <apexcharts stype="radialBar" :options="dataChartYear.chartOptions" :series="dataChartYear.series"></apexcharts>
        <div class="w-[70%] absolute left-[50%] translate-x-[-50%] bottom-0" v-if="useParseNumber(data.VPerYear) > 100">
          <apexcharts
            stype="radialBar"
            :options="dataChartChildYear.chartOptions"
            :series="dataChartChildYear.series"></apexcharts>
        </div>
        <div class="c-primary text-sm font-bold absolute left-[50%] translate-x-[-50%] bottom-16px">
          Năm {{ currentYear }}
        </div>
      </div>
      <div class="fc">
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Doanh thu</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.VolumeYear) }}</div>
        </div>
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Target</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.VYearTarget) }}</div>
        </div>
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Đạt</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.VPerYear) }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>
