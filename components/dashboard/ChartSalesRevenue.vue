<script lang="ts" setup>
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
})
const currentMonth = ref(new Date().getMonth())
const currentYear = ref(new Date().getFullYear())
const currentQuarter = ref(Math.ceil(new Date().getMonth() / 3))
const dataChart = computed(() => {
  return {
    chartOptions: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#F2F7F9',
            strokeWidth: '100%',
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: 0,
        },
      },
      fill: {
        colors: ['#FDBD10'],
      },
    },
    series: props.data.PerMonth ? [useParseNumber(props.data.PerMonth).toFixed()] : [0],
  }
})
const dataChartChild = computed(() => {
  return {
    chartOptions: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#F2F7F9',
            strokeWidth: '100%',
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: 0,
        },
      },
      fill: {
        colors: ['#F32246'],
      },
    },
    series:
      props.data.PerMonth && useParseNumber(props.data.PerMonth) > 100
        ? [useParseNumber(props.data.PerMonth) - 100]
        : [99],
  }
})
const dataChartQuater = computed(() => {
  return {
    chartOptions: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#F2F7F9',
            strokeWidth: '100%',
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: 0,
        },
      },
      fill: {
        colors: ['#FDBD10'],
      },
    },
    series: props.data.PerQua ? [useParseNumber(props.data.PerQua).toFixed()] : [0],
  }
})
const dataChartChildQuater = computed(() => {
  return {
    chartOptions: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#F2F7F9',
            strokeWidth: '100%',
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: 0,
        },
      },
      fill: {
        colors: ['#F32246'],
      },
    },
    series:
      props.data.PerQua && useParseNumber(props.data.PerQua) > 100 ? [useParseNumber(props.data.PerQua) - 100] : [99],
  }
})
const dataChartYear = computed(() => {
  return {
    chartOptions: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#F2F7F9',
            strokeWidth: '100%',
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: 0,
        },
      },
      fill: {
        colors: ['#FDBD10'],
      },
    },
    series: props.data.PerYear ? [useParseNumber(props.data.PerYear).toFixed()] : [0],
  }
})
const dataChartChildYear = computed(() => {
  return {
    chartOptions: {
      chart: {
        type: 'radialBar',
        offsetY: -20,
        sparkline: {
          enabled: true,
        },
      },
      plotOptions: {
        radialBar: {
          startAngle: -90,
          endAngle: 90,
          track: {
            background: '#F2F7F9',
            strokeWidth: '100%',
          },
          dataLabels: {
            name: {
              show: false,
            },
            value: {
              show: false,
            },
          },
        },
      },
      grid: {
        padding: {
          top: 0,
        },
      },
      fill: {
        colors: ['#F32246'],
      },
    },
    series:
      props.data.PerYear && useParseNumber(props.data.PerYear) > 100
        ? [useParseNumber(props.data.PerYear) - 100]
        : [99],
  }
})
</script>

<template>
  <div class="w-full grid grid-cols-3">
    <div class="fc ai-c">
      <div class="relative">
        <apexcharts stype="radialBar" :options="dataChart.chartOptions" :series="dataChart.series"></apexcharts>
        <div class="w-[70%] absolute left-[50%] translate-x-[-50%] bottom-0" v-if="useParseNumber(data.PerMonth) > 100">
          <apexcharts
            stype="radialBar"
            :options="dataChartChild.chartOptions"
            :series="dataChartChild.series"></apexcharts>
        </div>
        <div class="c-primary text-sm font-bold absolute left-[50%] translate-x-[-50%] bottom-16px">
          Tháng {{ currentMonth }}
        </div>
      </div>
      <div class="fc">
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Doanh thu</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.Monamt) }}</div>
        </div>
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Target</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.MonamtTarget) }}</div>
        </div>
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Đạt</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.PerMonth) }}%</div>
        </div>
      </div>
    </div>

    <div class="fc ai-c">
      <div class="relative">
        <apexcharts
          stype="radialBar"
          :options="dataChartQuater.chartOptions"
          :series="dataChartQuater.series"></apexcharts>
        <div class="w-[70%] absolute left-[50%] translate-x-[-50%] bottom-0" v-if="useParseNumber(data.PerQua) > 100">
          <apexcharts
            stype="radialBar"
            :options="dataChartChildQuater.chartOptions"
            :series="dataChartChildQuater.series"></apexcharts>
        </div>
        <div class="c-primary text-sm font-bold absolute left-[50%] translate-x-[-50%] bottom-16px">
          Quý {{ currentQuarter }}
        </div>
      </div>
      <div class="fc">
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Doanh thu</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.Quaamt) }}</div>
        </div>
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Target</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.QuaamtTarget) }}</div>
        </div>
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Đạt</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.PerQua) }}%</div>
        </div>
      </div>
    </div>

    <div class="fc ai-c">
      <div class="relative">
        <apexcharts stype="radialBar" :options="dataChartYear.chartOptions" :series="dataChartYear.series"></apexcharts>
        <div class="w-[70%] absolute left-[50%] translate-x-[-50%] bottom-0" v-if="useParseNumber(data.PerYear) > 100">
          <apexcharts
            stype="radialBar"
            :options="dataChartChildYear.chartOptions"
            :series="dataChartChildYear.series"></apexcharts>
        </div>
        <div class="c-primary text-sm font-bold absolute left-[50%] translate-x-[-50%] bottom-16px">
          Năm {{ currentYear }}
        </div>
      </div>
      <div class="fc">
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Doanh thu</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.Yeaamt) }}</div>
        </div>
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Target</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.YeaamtTarget) }}</div>
        </div>
        <div class="grid grid-cols-2 gap-4 ai-c">
          <div class="c-primary font-semibold">Đạt</div>
          <div class="c-title font-semibold">{{ useFormatNumber(data?.PerYear) }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>
