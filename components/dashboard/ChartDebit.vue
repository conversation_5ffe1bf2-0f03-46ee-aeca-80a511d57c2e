<script lang="ts" setup>
import { VcDonut } from 'vue-css-donut-chart'
import 'vue-css-donut-chart/dist/vcdonut.css'
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
})
const total = computed(() => 1000000)

const dataChart = computed(() => {
  return {
    sections: [
      {
        label: 'Công nợ trong 30 ngày',
        value: useParseNumber((props?.data?.Intime / props?.data?.TotalReceiable) * 100),
        sub: useParseNumber(props?.data?.Intime),
        color: '#004C82',
      },
      {
        label: 'Công nợ từ 31-60 ngày',
        value: useParseNumber((props?.data?.Less30 / props?.data?.TotalReceiable) * 100),
        sub: useParseNumber(props?.data?.Less30),
        color: '#FDBD10',
      },
      {
        label: 'Công nợ từ 61-90 ngày',
        value: useParseNumber((props?.data?.Less90 / props?.data?.TotalReceiable) * 100),
        sub: useParseNumber(props?.data?.Less90),
        color: '#00C3AD',
      },
      {
        label: 'Công nợ trên 90 ngày',
        value: useParseNumber(
          100 -
            (props?.data?.Intime / props?.data?.TotalReceiable) * 100 -
            (props?.data?.Less30 / props?.data?.TotalReceiable) * 100 -
            (props?.data?.Less90 / props?.data?.TotalReceiable) * 100,
        ),
        sub: useParseNumber(props.data?.More90),
        color: '#FC793F',
      },
    ],
  }
})
</script>

<template>
  <div class="fc">
    <div class="page com-heading">Tỉ trọng công nợ</div>
    <div class="grid grid-cols-5 gap-4">
      <vc-donut
        class="col-span-3"
        background="white"
        foreground="grey"
        :size="200"
        unit="px"
        :thickness="20"
        :has-legend="false"
        :sections="dataChart.sections"
        :total="100"
        :start-angle="0"
        auto-adjust-text-size
        :suppress-validation-warnings="false">
        <div class="fc ai-c jc-c text-sm">
          <div class="text-sm font-semibold c-black">Tổng nợ phải trả</div>
          <div class="text-base font-bold c-primary">
            {{ useFormatNumber(data?.TotalReceiable) }}
          </div>
        </div>
      </vc-donut>
      <div class="fc col-span-2">
        <ul class="">
          <li class="fr ai-fs gap-2" v-for="item in dataChart.sections">
            <div
              class="text-base w-10px h-10px min-w-10px rounded-full mt-5px"
              :style="{ backgroundColor: item.color }"></div>
            <div class="fc">
              <div class="text-sm c-title">{{ item.label }}</div>
              <div class="text-base c-primary font-bold">
                {{ useFormatNumber(item.sub) }}
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
:deep(span.cdc-legend-item-color) {
  border-radius: 50% !important;
  width: 10px !important;
  height: 10px !important;
}
</style>
