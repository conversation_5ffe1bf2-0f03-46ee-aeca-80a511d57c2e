<script lang="ts" setup>
import { useColorStatus } from '~/composables/useColorStatus'
import { useAppStore } from '~/stores/app'

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
})
const { statusColor } = useColorStatus()
const appStore = useAppStore()
</script>

<template>
  <div class="page">
    <div class="grid grid-cols-3 lg:grid-cols-7 gap-2">
      <div class="fr ai-c gap-2 bg-#8953571a rounded-lg px-1 py-1">
        <img src="~/assets/icons/i-status-10.svg" class="w-32px h-32px" alt="" />
        <div class="fc">
          <div class="text-xs font-semibold">Chưa gửi</div>
          <div class="text-base font-bold c-#895357">1</div>
        </div>
      </div>
      <div class="fr ai-c gap-2 bg-#fdbd101a rounded-lg px-1 py-1">
        <img src="~/assets/icons/i-status-20.svg" class="w-32px h-32px" alt="" />
        <div class="fc">
          <div class="text-xs font-semibold">Chờ xác nhận</div>
          <div class="text-base font-bold c-#fdbd10">1</div>
        </div>
      </div>
      <div class="fr ai-c gap-2 bg-#0095ff1a rounded-lg px-1 py-1">
        <img src="~/assets/icons/i-status-30.svg" class="w-32px h-32px" alt="" />
        <div class="fc">
          <div class="text-xs font-semibold">Đã xác nhận</div>
          <div class="text-base font-bold c-#0095ff">1</div>
        </div>
      </div>
      <div class="fr ai-c gap-2 bg-#fdbd101a rounded-lg px-1 py-1">
        <img src="~/assets/icons/i-status-35.svg" class="w-32px h-32px" alt="" />
        <div class="fc">
          <div class="text-xs font-semibold">Đang xử lý</div>
          <div class="text-base font-bold c-#fdbd10">1</div>
        </div>
      </div>
      <div class="fr ai-c gap-2 bg-#fc793f1a rounded-lg px-1 py-1">
        <img src="~/assets/icons/i-status-40.svg" class="w-32px h-32px" alt="" />
        <div class="fc">
          <div class="text-xs font-semibold">Đang giao</div>
          <div class="text-base font-bold c-#fc793f">1</div>
        </div>
      </div>
      <div class="fr ai-c gap-2 bg-#25c3001a rounded-lg px-1 py-1">
        <img src="~/assets/icons/i-status-50.svg" class="w-32px h-32px" alt="" />
        <div class="fc">
          <div class="text-xs font-semibold">Hoàn thành</div>
          <div class="text-base font-bold c-#25c300">1</div>
        </div>
      </div>
      <div class="fr ai-c gap-2 bg-#f3224633 rounded-lg px-1 py-1">
        <img src="~/assets/icons/i-status-60.svg" class="w-32px h-32px" alt="" />
        <div class="fc">
          <div class="text-xs font-semibold">Đã hủy</div>
          <div class="text-base font-bold c-#f32246">1</div>
        </div>
      </div>
    </div>
    <DataTable
      :value="data"
      dataKey="_id"
      rowHover
      scrollable
      :rows="1000"
      :loading="appStore.isLoading"
      scroll-height="300px">
      <Column header="#">
        <template #body="slotProps">
          {{ slotProps.index + 1 }}
        </template>
      </Column>

      <Column field="CreatedDate" header=" Ngày đặt" class="min-w-150px">
        <template #body="slotProps">
          <span class="text-base font-normal line-clamp-1">
            {{ useMoment(slotProps.data.OrderDate, 'YYYY-MM-DD') }}
          </span>
        </template>
      </Column>
      <Column field="DeliveryDate" header=" Ngày giao" class="min-w-150px">
        <template #body="slotProps">
          <span class="text-base font-normal line-clamp-1">
            {{ useMoment(slotProps.data.DeliveryDate, 'YYYY-MM-DD') }}
          </span>
        </template>
      </Column>
      <Column field="CustomerName" header="Nơi giao" class="min-w-300px">
        <template #body="slotProps">
          <span class="text-base font-normal line-clamp-1">
            {{ slotProps.data.CustomerName }}
          </span>
        </template>
      </Column>
      <Column field="TotalPrice" header="Tổng (VNĐ)" class="min-w-200px">
        <template #body="slotProps">
          <span class="text-base font-normal line-clamp-1">
            {{ useFormatNumber(slotProps.data.TotalPrice) }}
          </span>
        </template>
      </Column>
      <Column field="StatusText" header="Tình trạng" class="min-w-200px">
        <template #body="slotProps">
          <span class="text-base font-normal line-clamp-1" :style="`color: ${statusColor[slotProps.data.Status]}`">
            {{ slotProps.data.StatusText }}
          </span>
        </template>
      </Column>

      <template #empty>
        <EmptyState />
      </template>
    </DataTable>
  </div>
</template>
