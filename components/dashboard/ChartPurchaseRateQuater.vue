<script lang="ts" setup>
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
})
const dataChart = computed(() => {
  return {
    chartOptions: {
      chart: {
        height: 250,
        type: 'line',
        stacked: false,
        toolbar: false,
      },
      stroke: {
        width: [0, 2, 5],
        curve: 'smooth',
      },
      plotOptions: {
        bar: {
          columnWidth: '40%',
          borderRadius: 10,
        },
      },
      colors: ['#FDBD10', '#0095FF'],
      fill: {
        opacity: [0.85, 1],
        // colors: ['#FDBD10', '#0095FF'],
        gradient: {
          inverseColors: false,
          shade: 'light',
          type: 'vertical',
          opacityFrom: 0.85,
          opacityTo: 0.55,
          stops: [0, 100, 100, 100],
        },
      },
      yaxis: {
        labels: {
          formatter: function (y: any) {
            if (typeof y !== 'undefined') {
              return new Intl.NumberFormat('de-DE').format(y)
            }
            return y
          },
        },
      },
      labels: props.data?.QuarterDetailsSet
        ? props.data?.QuarterDetailsSet?.results?.map((el: any) => {
            return 'Q.' + el.QQuarter
          })
        : ['Quý 1', 'Quý 2', 'Quý 3', 'Quý 4'],
      markers: {
        size: 4,
      },
      tooltip: {
        shared: true,
        intersect: false,
        y: {
          formatter: function (y: any) {
            if (typeof y !== 'undefined') {
              return new Intl.NumberFormat('de-DE').format(y)
            }
            return y
          },
        },
      },
    },
    series: props.data?.MonthDetailsSet
      ? [
          {
            name: 'Năm nay',
            type: 'column',
            data: props.data?.QuarterDetailsSet.results.map((el: any) => {
              return Number(el.QCurAmt)
            }),
          },
          {
            name: 'Năm trước',
            type: 'line',
            data: props.data.QuarterDetailsSet.results.map((el: any) => {
              return Number(el.QPreAmt)
            }),
          },
        ]
      : [],
  }
})
</script>

<template>
  <apexcharts type="line" height="250" :options="dataChart.chartOptions" :series="dataChart.series"></apexcharts>
</template>
