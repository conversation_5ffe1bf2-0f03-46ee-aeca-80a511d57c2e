<script lang="ts" setup>
import ChartPurchaseRateMonth from './ChartPurchaseRateMonth.vue'
import ChartPurchaseRateQuater from './ChartPurchaseRateQuater.vue'
import ChartPurchaseRateYear from './ChartPurchaseRateYear.vue'
const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
})
const chartType = ref('month')
const listChartType = [
  {
    value: 'month',
    label: 'Tháng',
  },
  {
    value: 'quarter',
    label: 'Quý',
  },
  {
    value: 'year',
    label: 'Năm',
  },
]
</script>

<template>
  <div class="page w-full">
    <div class="com-heading fr jc-sb">
      Tỉ lệ mua hàng
      <div class="fr gap-2">
        <div
          class="text-base font-semibold c-black rounded-16px cursor-pointer px-3 py-1 transition-all"
          :class="{ '!c-white bg-primary': item.value === chartType }"
          @click="chartType = item.value"
          v-for="item in listChartType">
          {{ item.label }}
        </div>
      </div>
    </div>
    <ChartPurchaseRateMonth :data="data" v-if="chartType === 'month'" />
    <ChartPurchaseRateQuater :data="data" v-if="chartType === 'quarter'" />
    <ChartPurchaseRateYear :data="data" v-if="chartType === 'year'" />
  </div>
</template>
