<script lang="ts" setup>
import { logoALP, logoDT, logoFuji, logoREVO, logoYNGHUA } from '~/assets/images'
import { optionStatus, statusColor } from '~/constants'
import { useAppStore } from '~/stores/app'
const { $auth: auth, $dayjs: dayjs } = useNuxtApp()
const route = useRoute()

const query = ref({
  search: (route.query.search as string) || '',
  status: (Number(route.query.status) as number) || '',
  orderType: (route.query.orderType as string) || '',
  username: auth?.user?.username || undefined,
  sapCustomerId: auth?.user?.sapCustomerId || null,
  from: (route.query.from as string) || '',
  to: (route.query.to as string) || '',
})
const appStore = useAppStore()
const { isCustomer } = useRole()
const listOrder = ref([])
const listType = [
  {
    name: 'Tất cả',
    value: '',
  },
  {
    name: 'Mặc định',
    value: 'ZOR2',
  },
  {
    name: '<PERSON><PERSON><PERSON> điểm',
    value: 'ZFOC',
  },
]
const listStatus = ref([
  {
    name: 'Tất cả',
    value: '',
  },
  ...optionStatus,
])

const fakeData = [
  { brand: '001 - Dai Tan', total: 100000000, point: 50 },
  {
    brand: '002 - YH',
    total: 210000000,
    point: 70,
  },
  {
    brand: '003 - Fuji',
    total: 100000000,
    point: 10,
  },
  {
    brand: '004 - Revo',
    total: 400000000,
    point: 100,
  },
]
const mapBrand = (brand: any) => {
  switch (brand) {
    case '001 - Dai Tan':
      return {
        name: 'Đại Tân',
        logo: logoDT,
      }
    case '003 - Fuji':
      return {
        name: 'Fuji',
        logo: logoFuji,
      }
    case '004 - Revo':
      return {
        name: 'Revo',
        logo: logoREVO,
      }
    case '002 - YH':
      return {
        name: 'YNGHUA',
        logo: logoYNGHUA,
      }
    case '005 - ALP':
      return {
        name: 'An Lập Phát',
        logo: logoALP,
      }

    default:
      return {
        name: 'An Lập Phát',
        logo: logoALP,
      }
  }
}
const getData = async () => {
  appStore.isLoading = true
  const url = query.value.sapCustomerId
    ? `ZALP_SALES_ORDER_SRV/SOHeader?$filter=Customer eq '${query.value.sapCustomerId}'&sap_customer_id=${query.value.sapCustomerId}`
    : `ZALP_SALES_ORDER_SRV/SOHeader?$filter=UserName eq '${query.value.username}'`
  const { data }: any = await useApi(`sap_api_get`, {
    method: 'GET',
    params: {
      sap_api_url: url,
    },
  })
  listOrder.value = data.value?.d?.results || []
  appStore.isLoading = false
}
getData()

const dataFilter: any = computed(() => {
  const arr = listOrder.value.filter((item: any) => {
    return (
      (query.value.search ? item.OrderNumber.toLowerCase().includes(query.value.search.toLowerCase()) : true) &&
      (query.value.orderType ? item.OrderType == query.value.orderType : true) &&
      (query.value.status ? item.Status == query.value.status : true)
    )
  })

  return (
    arr.sort((a: any, b: any) => {
      return dayjs(b.OrderDate).valueOf() - dayjs(a.OrderDate).valueOf()
    }) || []
  )
})
watchDebounced(
  () => query.value.search,
  (newValue) => {
    useQueryURL(query.value)
  },
  {
    debounce: 300,
    maxWait: 4000,
  },
)
watchDebounced(
  () => query.value.status,
  (newValue) => {
    useQueryURL(query.value)
  },
  {
    debounce: 300,
    maxWait: 1000,
  },
)
watchDebounced(
  () => query.value.sapCustomerId,
  (newValue) => {
    getData()
  },
  {
    debounce: 300,
    maxWait: 1000,
  },
)
</script>

<template>
  <div class="min-h-80vh container mx-a fc gap-4">
    <div class="fr jc-c ai-c">
      <div class="page-heading text-center">Lịch sử tích điểm</div>
    </div>
    <div class="grid grid-cols-4 gap-4">
      <SelectCustomer :showClear="true" v-model:customer="query.sapCustomerId" v-if="!isCustomer" />
      <BaseInputCalendar label="Từ ngày" v-model="query.from" />
      <BaseInputCalendar label="Đến ngày" v-model="query.to" />
      <div class="fc jc-fe">
        <Button label="Xuất báo cáo" severity="primary" class="h-fit mb-2px"></Button>
      </div>
    </div>
    <div class="bg-white rounded p-4 fc gap-4">
      <div class="com-heading">Danh sách đơn hàng</div>
      <DataTable
        :value="fakeData"
        dataKey="_id"
        rowHover
        scrollable
        :rows="1000"
        :loading="appStore.isLoading"
        scroll-height="calc(100vh - 300px)">
        <Column header="#">
          <template #body="slotProps">
            {{ slotProps.index + 1 }}
          </template>
        </Column>
        <Column field="brand" header="Thương hiệu" class="min-w-200px">
          <template #body="slotProps">
            <div class="fr ai-c gap-2">
              <img :src="mapBrand(slotProps.data.brand).logo" alt="" class="w-20px" />
              <span class="text-base font-normal line-clamp-1">
                {{ mapBrand(slotProps.data.brand).name }}
              </span>
            </div>
          </template>
        </Column>

        <Column field="total" header="Tổng (VNĐ)" class="min-w-200px">
          <template #body="slotProps">
            <span class="text-base font-normal line-clamp-1">
              {{ useFormatNumber(slotProps.data.total) }}
            </span>
          </template>
        </Column>
        <Column field="point" header="Số điểm quy đổi" class="min-w-300px w-300px">
          <template #body="slotProps">
            <span class="text-base font-normal line-clamp-1">
              {{ useFormatNumber(slotProps.data.point) }}
            </span>
          </template>
        </Column>

        <template #empty>
          <EmptyState />
        </template>
      </DataTable>
      <div class="fr jc-sb ai-c">
        <div class="">
          <div class="text-lg font-bold">Tổng điểm tích lũy:</div>
        </div>
        <div class="w-300px pl-18px">
          <div class="text-lg font-bold">{{ useFormatNumber(230) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
