<script setup lang="ts">
import { useForm } from 'vee-validate'

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  listRoles: {
    type: Array,
    required: true,
    default: () => [],
  },
  isEdit: {
    type: Boolean,
    required: false,
  },
})

const form = ref<any>({
  email: '',
  password: '',
  phone: '',
  name: '',
  role_id: undefined,
  is_active: true,
  ...props.data,
})

const emit = defineEmits(['onSubmit'])

const { handleSubmit } = useForm()

const onSubmit = handleSubmit(() => {
  emit('onSubmit', { ...toRaw(form.value) })
})

watch(
  () => props.data,
  (newValue) => {
    form.value = { ...newValue }
  },
  { deep: true },
)
</script>

<template>
  <form autocomplete="off" @submit.prevent="onSubmit">
    <BaseInputText class="flex-1 mb-3" name="name" label="Name" :rules="{ required: true }" v-model="form.name" />
    <BaseInputText class="flex-1 mb-3" name="email" label="Email" :rules="{ required: true }" v-model="form.email" />
    <BaseInputText class="flex-1 mb-3" name="phone" label="Phone" :rules="{ required: false }" v-model="form.phone" />
    <BaseInputPassword
      class="flex-1 mb-3"
      name="password"
      label="Password"
      :rules="{ required: !isEdit ? true : false }"
      v-model="form.password" />
    <BaseInputSelect
      class="flex-1 mb-3"
      name="role"
      label="Role"
      :options="listRoles"
      optionLabel="name"
      optionValue="_id"
      :rules="{ required: true }"
      v-model="form.role_id" />
    <BaseSwitch class="flex-1 mb-3" name="is_active" label="Active" v-model="form.is_active" />
    <div class="flex justify-end gap-4">
      <Button type="button" label="Cancel" severity="cancel" @click="$router.go(-1)" />
      <Button type="submit" :label="!isEdit ? 'Create' : 'Update'" severity="primary" />
    </div>
  </form>
</template>
