<script setup lang="ts">
import { listPermissions } from 'constants/list-permission'
import { useForm } from 'vee-validate'

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  isEdit: {
    type: Boolean,
    required: false,
  },
})

const emit = defineEmits(['onSubmit'])

const { handleSubmit } = useForm()

const form = ref<any>({
  ...props.data,
})
const selectedKey = ref({})

const mapSelectedKey = (array: any) => {
  const obj = {}
  array.forEach((key: any) => {
    //@ts-ignore
    obj[key] = {
      checked: true,
      partialChecked: true,
    }
  })
  selectedKey.value = obj
}

const onSubmit = handleSubmit(() => {
  emit('onSubmit', { ...toRaw(form.value) })
})
if (props.data?.permissions) {
  mapSelectedKey(props.data?.permissions)
}

watch(
  () => props.data,
  (newValue) => {
    form.value = { ...newValue }
    mapSelectedKey(newValue?.permissions)
  },
  { deep: true },
)
watch(
  () => form.vale?.permissions,
  (newValue) => {},
  { deep: true },
)
const onNodeSelect = (node: any) => {
  form.permissions.value.push(node.key)
}
const onNodeUnSelect = (node: any) => {
  form.permissions.value.filter(node.key)
}
watch(
  () => selectedKey.value,
  (val) => {
    const array = Object.keys(selectedKey.value)
    form.value.permissions = array
  },
)
</script>

<template>
  <form autocomplete="off" @submit.prevent="onSubmit">
    <BaseInputText class="flex-1 mb-3" name="name" label="Name" :rules="{ required: true }" v-model="form.name" />
    <div class="text-m mb-1">Permission</div>

    <Tree
      v-model:selectionKeys="selectedKey"
      :value="listPermissions"
      @node-select="onNodeSelect"
      @node-unselect="onNodeUnSelect"
      selectionMode="checkbox"
      class="w-full mb-3">
    </Tree>
    <BaseCheckbox class="mb-3" name="agree" :rules="{ required: false }" v-model="form.is_admin">
      <template #content>
        <p class="text-base font-normal c-gray-50">Is Admin</p>
      </template>
    </BaseCheckbox>
    <div class="flex justify-end gap-4">
      <Button type="button" label="Cancel" severity="cancel" @click="$router.go(-1)" />
      <Button type="submit" :label="!isEdit ? 'Create' : 'Update'" severity="primary" />
    </div>
  </form>
</template>
