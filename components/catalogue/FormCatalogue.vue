<script lang="ts" setup>
import { useForm } from 'vee-validate'
import { listBrand } from '~/constants/brand'
const form = defineModel<any>('form')
const props = defineProps({
  isLoading: {
    type: Boolean,
    required: false,
  },
})
const { handleSubmit } = useForm()

const emits = defineEmits(['submit', 'cancel', 'edit'])
const isEdit = computed(() => form.value.id)
const onSubmit = handleSubmit(() => {
  // isEdit.value ? emits('edit', { ...toRaw(form.value) }) : emits('submit', { ...toRaw(form.value) })
  const formData = new FormData()
  formData.append('file', form.value.file)
  formData.append('thumbnail', form.value.image)

  formData.append(
    'data',
    new Blob(
      [
        JSON.stringify({
          title: form.value.title,
          brandName: form.value.brandName,
          objectType: 'CUSTOMER',
        }),
      ],
      {
        type: 'application/json',
      },
    ),
  )
  emits('submit', formData)
})

watch(
  () => form.value,
  (newValue) => {
    if (!newValue.images) {
      form.value.images = []
    }
  },
  { deep: true, immediate: true },
)
</script>

<template>
  <form class="grid grid-cols-1 lg:grid-cols-2 gap-4 p-4" @submit.prevent="onSubmit">
    <BaseInputText class="flex-1 mb-3" name="title" label="Tên" :rules="{ required: true }" v-model="form.title" />
    <BaseInputSelect
      v-model="form.brandName"
      name="brandName"
      placeholder="Chọn"
      :options="listBrand"
      option-label=""
      label="Thương hiệu"
      option-value=""
      :rules="{ required: true }" />
    <div class="fc gap-2">
      <div class="text-base c-black-90">Hình thumbnail</div>
      <input type="file" accept="image/*" @change="(e:any) => (form.image = e.target.files[0])" />
    </div>
    <div class="fc gap-2">
      <div class="text-base c-black-90">File</div>
      <input type="file" accept="application/pdf" @change="(e:any) => (form.file = e.target.files[0])" />
    </div>

    <div class="col-span-2 fr jc-fe gap-4">
      <Button severity="cancel" @click="emits('cancel')">Hủy</Button>
      <Button severity="primary" type="submit" :loading="isLoading">{{ !isEdit ? 'Tạo' : 'Lưu' }}</Button>
    </div>
  </form>
</template>
