<script lang="ts" setup>
import { useForm } from 'vee-validate'
const form = defineModel<any>('form')

const { handleSubmit } = useForm()

const emits = defineEmits(['submit', 'cancel'])
const isEdit = computed(() => form.value._id)
const onSubmit = handleSubmit(() => {
  emits('submit', { ...toRaw(form.value) })
})
</script>

<template>
  <form class="grid grid-cols-1 lg:grid-cols-2 gap-4 p-4" @submit.prevent="onSubmit">
    <BaseInputText
      class="flex-1 mb-3"
      name="name"
      label="Name"
      :rules="{ required: true }"
      v-model="form.fullName"
      :disabled="true" />
    <BaseInputPassword
      class="flex-1 mb-3"
      name="password"
      label="Password"
      :rules="{ required: !isEdit ? true : false }"
      v-model="form.password" />
    <div class="col-span-2 fr jc-fe gap-4">
      <Button severity="cancel" @click="emits('cancel')">Hủy</Button>
      <Button severity="primary" type="submit">Lưu</Button>
    </div>
  </form>
</template>
