<script lang="ts" setup>
import { useAppStore } from '~/stores/app'
const props = defineProps({
  showClear: {
    type: Boolean,
    default: false,
  },
})

const appStore = useAppStore()
const search = ref<string>('')
const listCustomer = computed(() => appStore.listCustomer)
const emits = defineEmits(['update:modelValue'])
const listNewMaterial = ref<any>([])
const form = ref({
  material: '',
})
const changeSearch = (event: any) => {
  search.value = event
}
const sapCustomerId = defineModel('customer', {
  default: [] as any,
})
const onSearch = () => {
  listNewMaterial.value = listCustomer.value
    .filter((el: any) => {
      return el.FullName.includes(search.value) || el.street.includes(search.value)
    })
    .slice(0, 200)
}
watch(
  () => listCustomer.value,
  (newValue) => {
    onSearch()
  },
  {
    immediate: true,
  },
)

watchDebounced(
  () => search.value,
  (newValue) => {
    onSearch()
  },
  { debounce: 300, maxWait: 1000 },
)
</script>

<template>
  <div class="w-full">
    <BaseInputSelect
      placeholder="Chọn"
      :options="listNewMaterial"
      optionLabel="FullName"
      optionValue="CustomerID"
      name="customer"
      v-model="sapCustomerId"
      :isSearch="true"
      :showClear="showClear"
      @search="changeSearch"
      label="Khách hàng" />
  </div>
</template>
