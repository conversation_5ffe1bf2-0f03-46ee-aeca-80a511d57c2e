<script lang="ts" setup>
import { logoDefault } from '~/assets/images'
import { useAppStore } from '~/stores/app'

const props = defineProps({
  item: {
    type: Object,
    required: true,
    default: () => {},
  },
  isDisabled: {
    type: Boolean,
    required: false,
    default: false,
  },
})

const appStore = useAppStore()
const listImageStore = computed(() => appStore.listImage)
const selectedImage = ref('')
const listImage = ref<any>([])
const confirm = useConfirm()
const toast = useToast()
const decreaseQuantity = () => {
  appStore.decreaseQuantity(props.item)
}
const increaseQuantity = () => {
  appStore.increaseQuantity(props.item)
}
const removeItem = () => {
  confirm.require({
    message: 'Bạn có chắc chắn muốn xóa sản phẩm này không?',
    header: '<PERSON><PERSON><PERSON> nhận',
    icon: 'pi pi-exclamation-triangle',
    rejectLabel: 'Hủy',
    acceptLabel: 'Xóa',
    acceptClass: 'p-button-danger',
    rejectClass: 'p-button-help',
    accept: () => {
      appStore.removeItem(props.item)
    },
  })
}
const mapImage = () => {
  listImage.value =
    listImageStore.value
      .filter(
        (item: any) =>
          item.salesMatNumber == props.item.Salesmatnumber && item.materialNumber == props.item.Materialnumber,
      )
      ?.map((item: any) => item.images)[0] || []

  if (listImage.value.length > 0) {
    selectedImage.value = listImage.value[0]?.url
  }
}
watch(
  () => props.item,
  (newValue) => {
    mapImage()
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <div class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
    <!-- Product Image -->
    <div class="flex-shrink-0">
      <img
        :src="selectedImage ? selectedImage : logoDefault"
        :alt="item.name"
        class="w-20 h-20 object-cover rounded-lg" />
    </div>

    <!-- Product Info -->
    <div class="flex-1 min-w-0">
      <h3 class="text-lg font-medium c-black line-clamp-2">{{ item?.MaterialDescription }}</h3>
      <div class="mt-1 text-sm text-gray-600">
        <p v-if="item?.Variationtype == 'C'">
          Màu: {{ item?.Variationvalue }} {{ item?.Colorname ? `- ${item?.Colorname}` : '' }}
        </p>
        <p v-if="item?.Variationtype == 'S'">Size: {{ item?.Variationvalue }}</p>
      </div>
      <div class="mt-2 flex items-center space-x-4">
        <span class="text-lg font-semibold text-blue-600">{{ useFormatNumber(item?.Points) }} điểm</span>
      </div>
      <!-- Quantity Controls -->
      <div class="flex items-center space-x-3">
        <button
          @click="decreaseQuantity()"
          :disabled="item.quantity <= 1 || isDisabled"
          class="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-full hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
          </svg>
        </button>
        <span class="w-8 text-center font-medium">{{ item?.quantity }}</span>
        <button
          :disabled="isDisabled"
          @click="increaseQuantity()"
          class="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-full hover:bg-gray-50">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Item Total -->
    <div class="text-right">
      <p class="text-lg font-semibold text-gray-900">
        {{ useFormatNumber(useParseNumber(item?.Points) * item.quantity) }} điểm
      </p>
    </div>

    <!-- Remove Button -->
    <button @click="removeItem()" class="text-red-500 hover:text-red-700 p-2" v-if="!isDisabled">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
      </svg>
    </button>
  </div>
</template>
