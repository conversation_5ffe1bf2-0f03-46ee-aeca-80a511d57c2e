<script setup lang="ts">
defineProps({
  visible: {
    type: Boolean,
    required: true,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  width: {
    type: [String, Number],
    default: '50vw'
  },
  showIconClose: {
    type: Boolean,
    default: false,
  }
})

const emit = defineEmits(['onClose'])

const closeDialog = () => {
  emit('onClose')
}

</script>

<template>
  <Dialog class="[&>.p-dialog-content]:py-3 [&>.p-dialog-content]:px-3 [&>.p-dialog-content]:rounded" modal
    :breakpoints="{ '960px': '75vw', '641px': '90vw' }" :showHeader="false" :style="{ width }" :closable="showIconClose"
    :dismissableMask="true" :visible="visible" @update:visible="closeDialog" @click.stop>
    <h3 class="mb-3 text-2xl font-bold c-black-90 text-center" v-if="title">
      {{ title }}
    </h3>
    <slot></slot>
  </Dialog>
</template>