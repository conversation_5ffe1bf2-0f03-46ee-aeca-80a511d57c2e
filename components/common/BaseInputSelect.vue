<script setup lang="ts">
import { useField } from 'vee-validate'

const props = defineProps({
  name: {
    type: String,
    default: '',
  },
  modelValue: {
    type: [String, null, Number] as PropType<String | null | Number>,
    required: true,
  },
  placeholder: {
    type: String,
    default: 'Chọn',
  },
  options: {
    type: Array,
    required: true,
  },
  optionLabel: {
    type: String,
    default: 'name',
  },
  optionValue: {
    type: String,
    default: 'value',
  },
  label: {
    type: String,
  },
  filter: {
    type: Boolean,
    default: false,
  },
  rules: {
    type: Object,
    default: () => ({}),
  },
  disabled: {
    type: <PERSON><PERSON><PERSON>,
    required: false,
  },
  // This prop was passed by @scroll in  component parent
  onScroll: {
    type: Function,
  },
  isSearch: {
    type: Boolean,
    default: false,
  },
  showClear: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'search', 'scroll'])

const search = ref('')

const rules = computed(() => props.rules)

const { value, errorMessage, handleBlur } = useField(() => props.name, rules, {
  initialValue: props.modelValue as String,
  syncVModel: true,
})

const handleScroll = useThrottleFn((e) => {
  emit('scroll')
}, 400)

watch(
  () => search.value,
  (newValue) => {
    emit('search', newValue)
  },
)
</script>

<template>
  <div class="flex flex-col gap-2">
    <label class="text-base font-normal c-black-90" :for="props.name" v-if="label">
      {{ label }} <span class="c-danger" v-if="rules.required">*</span>
    </label>
    <Dropdown
      :inputId="props.name"
      :class="[errorMessage ? 'p-invalid' : '']"
      :placeholder="placeholder"
      :options="options"
      :optionLabel="optionLabel"
      :optionValue="optionValue"
      :showClear="showClear"
      :filter="filter"
      class="h-44.5px"
      v-model="value"
      @blur="handleBlur($event, true)"
      :disabled="disabled"
      :virtualScrollerOptions="onScroll ? ({ itemSize: 48, onScroll: handleScroll } as any) : undefined">
      <!-- Search -->
      <template #header v-if="isSearch">
        <InputSearch class="mt-1 mb-2 border-0 border-b-1px border-b-#ccc rounded-none" v-model="search" />
      </template>
      <template #empty>Không có dữ liệu</template>
    </Dropdown>
    <small class="p-error" v-show="errorMessage">{{ errorMessage || '&nbsp;' }}</small>
  </div>
</template>
