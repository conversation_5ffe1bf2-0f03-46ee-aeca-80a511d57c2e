<script lang="ts" setup>
import { logo, logoALP, logoDefault, logoDT, logoFuji, logoREVO, logoYNGHUA } from '~/assets/images'
import { useAppStore } from '~/stores/app'

const props = defineProps({
  product: {
    type: Object,
    required: true,
  },
})
const appStore = useAppStore()
const images = computed(() => {
  {
    const list = appStore.listImage.filter((item: any) => item.salesMatNumber === props.product.Salesmatnumber)

    if (list.length > 0) {
      const image = list[0].images.map((el: any) => el.url)
      return image
    } else return []
  }
})
const mapBrand = (brand: any) => {
  switch (brand) {
    case '001 - Dai Tan':
      return {
        name: '<PERSON><PERSON><PERSON>ân',
        logo: logoDT,
      }
    case '003 - Fuji':
      return {
        name: '<PERSON>',
        logo: logoFuji,
      }
    case '004 - Revo':
      return {
        name: 'Revo',
        logo: logoREVO,
      }
    case '002 - YH':
      return {
        name: 'YNG<PERSON>U<PERSON>',
        logo: logoY<PERSON><PERSON>U<PERSON>,
      }
    case '005 - ALP':
      return {
        name: '<PERSON>',
        logo: logoALP,
      }

    default:
      return {
        name: '<PERSON>',
        logo: logoALP,
      }
  }
}
</script>

<template>
  <nuxt-link
    :to="`/product/${product.Materialnumber}?Salesmatnumber=${product.Salesmatnumber}`"
    class="flex fc bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer group">
    <!-- Image -->
    <div class="relative aspect-square overflow-hidden">
      <img
        v-if="images.length > 0"
        :src="images[0]"
        :alt="product.PortalDescription"
        class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" />
      <img
        v-else
        :src="logoDefault"
        :alt="product.PortalDescription"
        class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" />
    </div>

    <!-- Content -->
    <div class="p-5 fc flex-1">
      <h3 class="text-lg font-bold c-black mb-2 line-clamp-2 group-hover:c-primary transition-colors">
        {{ product.PortalDescription }}
      </h3>
      <p class="text-gray-600 text-sm mb-2 line-clamp-3">
        {{ product.MaterialDescription }}
      </p>
      <div class="grid grid-cols-2">
        <div class="fr ai-c jc-fs gap-1">
          <div
            class="text-md font-medium uppercase text-gray-500 w-20px h-20px rounded-full min-w-20px min-h-20px"
            v-if="product?.Colorcode?.length && product?.Variationtype === 'C'"
            v-for="(item, index) in product?.Colorcode"
            :style="{ background: '#' + item }"></div>
        </div>
        <div class="fr ai-c jc-fe">
          <div
            class="text-md font-medium uppercase text-gray-500"
            v-if="product?.Variationvalue?.length && product?.Variationtype === 'S'"
            v-for="(item, index) in product?.Variationvalue">
            {{ item }} {{ index !== product?.Variationvalue?.length - 1 ? ' - ' : '' }}
          </div>
        </div>
      </div>
      <p class="mt-2 text-lg font-bold c-black mb-2 line-clamp-2 group-hover:c-primary transition-colors">
        {{ useFormatNumber(product.Points) }} điểm
      </p>

      <!-- Author & Date -->
      <div class="flex items-end justify-end flex-1">
        <img :src="mapBrand(product.Brand).logo" class="h-36px w-auto object-cover" alt="" />
      </div>
    </div>
  </nuxt-link>
</template>
