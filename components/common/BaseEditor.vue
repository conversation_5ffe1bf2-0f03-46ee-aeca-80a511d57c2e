<script setup lang="ts">
import { useField } from 'vee-validate'
// @ts-ignore
import ClassicEditor from '@ckeditor/ckeditor5-build-classic'
const props = defineProps({
  name: {
    type: String,
    default: '',
  },
  modelValue: {
    type: [String, null] as PropType<String | null>,
    required: true,
  },
  placeholder: {
    type: String,
    default: 'Enter',
  },
  label: {
    type: String,
  },
  styleInput: {
    type: Object,
    default: () => ({}),
  },
  classInput: {
    type: [String, Object, Array],
    default: '',
  },
  rules: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue'])
const useConfig = useRuntimeConfig()
const { $auth: auth }: any = useNuxtApp()

class MyUploadAdapter {
  public loader

  constructor(loader: any) {
    // Save Loader instance to update upload progress.
    this.loader = loader
  }

  async upload() {
    const file = await this.loader.file
    const formData = new FormData()
    formData.append('files', file)
    formData.append('resourceType', 'NEWS_CONTENT')

    return new Promise(async (resolve, reject) => {
      await fetch(useConfig.public.apiBase + `/user:upload-files`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${auth.strategy?.token.get()}`,
        },
        body: formData,
      })
        .then((response) => response.json())
        .then((data) => {
          console.log(data, 'data')
          if (data?.data) {
            resolve({ default: data.data[0].url })
          }
        })
    })
  }

  abort() {
    // Reject promise returned from upload() method.
  }
}

function MyCustomUploadAdapterPlugin(editor: any) {
  editor.plugins.get('FileRepository').createUploadAdapter = (loader: any) => {
    // Configure the URL to the upload script in your back-end here!
    return new MyUploadAdapter(loader)
  }
}

const config = ref({
  extraPlugins: [MyCustomUploadAdapterPlugin],
  toolbar: {
    items: [
      'heading',
      '|',
      'bold',
      'italic',
      'link',
      'bulletedList',
      'numberedList',
      '|',
      'outdent',
      'indent',
      '|',
      'imageUpload',
      'blockQuote',
      'insertTable',
      'mediaEmbed',
      'undo',
      'redo',
    ],
  },
  language: 'en',
  image: {
    toolbar: ['imageTextAlternative', 'toggleImageCaption', 'imageStyle:inline', 'imageStyle:block', 'imageStyle:side'],
  },
  table: {
    contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells'],
  },
  mediaEmbed: {
    previewsInData: true,
  },
})

const { value, errorMessage, handleBlur } = useField(() => props.name, props.rules, {
  initialValue: props.modelValue as string,
  syncVModel: true,
})
</script>

<template>
  <div class="flex flex-col gap-2">
    <label class="text-base font-normal c-black-90" :for="props.name" v-if="label">
      {{ label }} <span class="c-danger" v-if="rules.required">*</span>
    </label>
    <ckeditor
      :id="props.name"
      :class="[classInput, errorMessage ? 'p-invalid' : '']"
      :style="styleInput"
      :editor="ClassicEditor"
      :config="config"
      :placeholder="placeholder"
      v-model="value"
      @blur="handleBlur($event, true)" />
    <small class="p-error" v-show="errorMessage">{{ errorMessage || '&nbsp' }}</small>
  </div>
</template>

<style lang="scss" scoped>
:deep {
  // .ck-source-editing-area {
  //   min-height: 300px;
  // }

  .ck-content {
    min-height: 300px;
    // h1,
    // h2,
    // h3,
    // h4,
    // p,
    // b,
    // i,
    // s,
    // u,
    // ul,
    // li,
    // ol,
    // a {
    //   all: revert;
    // }
  }
}
</style>
