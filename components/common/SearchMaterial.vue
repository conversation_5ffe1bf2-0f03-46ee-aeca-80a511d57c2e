<script lang="ts" setup>
import xoaDau from '~/constants/convertVietnamese'
import { useAppStore } from '~/stores/app'

const appStore = useAppStore()
const search = ref<string>('')
// const props = defineProps({
//   modelValue: {
//     type: String,
//     default: [],
//   },
// })
const listMaterial = computed(() => appStore.listMaterialSearch)
const emits = defineEmits(['update:modelValue'])
const listNewMaterial = ref<any>([])
const form = ref({
  material: '',
})
const changeSearch = (event: any) => {
  search.value = event
}
const material = defineModel('material', {
  default: [] as any,
})
const onSearch = () => {
  const lists = listMaterial.value.filter((el: any) => {
    return material.value.includes(el.materialNumber)
  })
  listNewMaterial.value = [
    ...lists,
    ...listMaterial.value
      .filter((el: any) => {
        return el.label.toLowerCase().includes(search.value.toLowerCase())
      })
      .slice(0, 200),
  ]
}
watch(
  () => listMaterial.value,
  (newValue) => {
    onSearch()
  },
  {
    immediate: true,
  },
)
watchDebounced(
  () => search.value,
  (newValue) => {
    onSearch()
  },
  { debounce: 300, maxWait: 1000 },
)
</script>

<template>
  <div class="w-full">
    <BaseInputSelectMultiple
      placeholder="Chọn"
      :options="listNewMaterial"
      optionLabel="name"
      optionValue="materialNumber"
      v-model="material"
      :isSearch="true"
      @search="changeSearch"
      label="Mã sản phẩm" />
  </div>
</template>
