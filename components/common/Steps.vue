<script setup lang="ts">

type StepItem = {
  title: string
}

const props = defineProps({
  steps: {
    type: Array as PropType<StepItem[]>,
    required: true,
    default: () => ([])
  },
  currentStep: {
    type: Number,
    required: true,
    default: '',
  }
})

const styleCircleByStep = (number: number) => {
  const step = props.currentStep
  if (number === step) {
    return 'bg-white border-primary'
  } else if (number < step) {
    return 'bg-primary border-primary'
  } else {
    return ''
  }
}

</script>

<template>
  <div class="flex">
    <div class="flex-1 relative" v-for="(item, index) in steps" :key="index">
      <div class="flex flex-col items-center gap-2">
        <span class="text-base font-normal c-black-60 transition-all line-clamp-1"
          :class="{ '!c-primary': index + 1 === currentStep }">
          {{ item.title }}
        </span>
        <div
          class="relative w-[16px] h-[16px] bg-black-30 border-2 border-black-30 border-solid rounded-full transition-all z-1"
          :class="styleCircleByStep(index + 1)"></div>
      </div>
      <div class="absolute bottom-8 left-[50%] w-full h-[1px] bg-gray-20 transition-all"
        :class="index + 1 < currentStep ? ['!bg-primary'] : ''" v-if="index < steps.length - 1">
      </div>
    </div>
  </div>
</template>