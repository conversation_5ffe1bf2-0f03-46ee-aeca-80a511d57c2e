<script setup lang="ts">
import { useField } from 'vee-validate'

const props = defineProps({
  name: {
    type: String,
    default: '',
  },
  modelValue: {
    type: [Number, null] as PropType<Number | null>,
    required: true,
  },
  placeholder: {
    type: String,
    default: 'Enter',
  },
  label: {
    type: String,
  },
  styleInput: {
    type: Object,
    default: () => ({}),
  },
  classInput: {
    type: [String, Object, Array],
    default: '',
  },
  // Grouping separator, ex: 100,000
  useGrouping: {
    type: Boolean,
    default: false,
  },
  rules: {
    type: Object,
    default: () => ({}),
  },
  disabled: {
    type: Boolean,
    required: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const rules = computed(() => props.rules)

const { value, errorMessage, handleBlur } = useField(() => props.name, rules, {
  initialValue: props.modelValue as number,
  syncVModel: true,
})
</script>

<template>
  <div class="w-full flex flex-col gap-2">
    <label class="text-base font-normal c-black-90" :for="props.name" v-if="label">
      {{ label }} <span class="c-danger" v-if="rules.required">*</span>
    </label>
    <InputNumber
      :inputId="props.name"
      :class="[classInput, errorMessage ? 'p-invalid' : '']"
      :style="styleInput"
      :placeholder="placeholder"
      :useGrouping="useGrouping"
      :allowEmpty="true"
      v-model="value"
      @blur="handleBlur(undefined, true)"
      :disabled="disabled" />
    <small class="p-error" v-show="errorMessage">{{ errorMessage || '&nbsp;' }}</small>
  </div>
</template>
<style lang="scss" scoped>
:deep(input#quantity) {
  width: 100% !important;
}
</style>
