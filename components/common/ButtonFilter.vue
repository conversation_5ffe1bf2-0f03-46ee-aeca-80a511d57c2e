<script setup lang="ts">

const emit = defineEmits(['onClearAll', 'onApply'])

const overlayPanel = ref()

const toggleOverlayPanel = (e: Event) => {
  overlayPanel.value.toggle(e)
}

const hideOverlayPanel = () => {
  overlayPanel.value.hide()
}

const onClearAll = () => {
  hideOverlayPanel()
  emit('onClearAll')
}

const onApply = () => {
  hideOverlayPanel()
  emit('onApply')
}

</script>

<template>
  <Button class="!bg-primary-20 !border-primary-20 !c-primary hover:opacity-70" type="button" size="small" label="Filter"
    @click="toggleOverlayPanel">
    <template #icon>
      <img src="~/assets/icons/i-filter-primary.svg" alt="">
    </template>
  </Button>

  <OverlayPanel class="!mt-2 min-w-[445px]" ref="overlayPanel">
    <slot name="body"></slot>
    <div class="flex gap-4">
      <Button class="flex-1" label="Clear all" severity="cancel" type="button" @click="onClearAll" />
      <Button class="flex-1" label="Apply" severity="apply" type="button" @click="onApply" />
    </div>
  </OverlayPanel>
</template>