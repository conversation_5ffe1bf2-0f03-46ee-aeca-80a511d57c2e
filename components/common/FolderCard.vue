<script setup lang="ts">

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
})

const overlayPanel = ref()

const toggleOverlayPanel = (e: Event) => {
  overlayPanel.value.toggle(e)
}

</script>

<template>
  <div class="p-[12px] flex items-center gap-3 bg-white border-1 border-gray-20 border-solid rounded-lg cursor-pointer">
    <img class="icon-xl" src="~/assets/icons/i-folder.svg" alt="">
    <div class="flex-1">
      <p class="mb-0 text-base font-semibold c-gray-50 line-clamp-1">
        {{ data?.name }}
      </p>
      <p class="text-sm font-normal c-gray-40">
        {{ data?.files?.length }} items
      </p>
    </div>

    <button @click.stop="toggleOverlayPanel" v-if="$slots.actions">
      <img src="~/assets/icons/i-more-vertical-gray.svg" alt="">
    </button>

    <OverlayPanel class="-ml-1 min-w-[224px] [&>.p-overlaypanel-content]:p-0 !before:-ml-[18px] !after:-ml-[16px]"
      ref="overlayPanel">
      <slot name="actions"></slot>
    </OverlayPanel>
  </div>
</template>