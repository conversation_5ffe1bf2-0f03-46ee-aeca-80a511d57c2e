<script setup lang="ts">
import { useField } from 'vee-validate'

const props = defineProps({
  name: {
    type: String,
    default: '',
  },
  modelValue: {
    type: [Date, String, null, undefined] as PropType<Date | string | null | undefined>,
    required: true,
  },
  placeholder: {
    type: String,
    default: 'Select',
  },
  label: {
    type: String,
  },
  type: {
    type: String as PropType<'date' | 'month' | 'year'>,
    default: 'date',
  },
  dateFormat: {
    type: String,
    default: 'yy/mm/dd',
  },
  rules: {
    type: Object,
    default: () => ({}),
  },
  disabled: {
    type: Boolean,
    required: false,
  },
  minDate: {
    type: [Date, undefined] as PropType<Date | undefined>,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue'])

const rules = computed(() => props.rules)

const { value, errorMessage, handleBlur } = useField(() => props.name, rules, {
  initialValue: props.modelValue ? useMoment(props.modelValue, 'YYYY-MM-DD') : (props.modelValue as string | Date),
  syncVModel: true,
})
</script>

<template>
  <div class="flex flex-col gap-2">
    <label class="text-base font-normal c-black-90" :for="props.name" v-if="label">
      {{ label }} <span class="c-danger" v-if="rules.required">*</span>
    </label>
    <Calendar
      :inputId="props.name"
      :class="[errorMessage ? 'p-invalid' : '']"
      :placeholder="placeholder"
      :view="type"
      showIcon
      :dateFormat="dateFormat"
      :minDate="minDate"
      v-model="value"
      @blur="handleBlur(undefined, true)"
      :disabled="disabled" />
    <small class="p-error" v-show="errorMessage">{{ errorMessage || '&nbsp;' }}</small>
  </div>
</template>
