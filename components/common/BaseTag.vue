<script setup lang="ts">
import { colorStatus } from '~/constants'

const props = defineProps({
  status: {
    type: String,
    default: ''
  },
  text: {
    type: String,
    default: ''
  }
})

const colorTagByStatus = computed(() => {
  return colorStatus[props.status as keyof typeof colorStatus] || {}
})

</script>

<template>
  <div class="py-[4px] px-[12px] text-center rounded" :style="{ background: colorTagByStatus.background }">
    <span class="text-base font-normal capitalize c-black-90" :style="{ color: colorTagByStatus.color }">
      {{ text }}
    </span>
  </div>
</template>