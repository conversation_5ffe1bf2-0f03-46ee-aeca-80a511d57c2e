<script setup lang="ts">
import { AvatarDefault } from '~/assets/images'

defineProps({
  size: {
    type: [String, Number],
    default: 32
  },
  url: {
    type: String
  },
  type: {
    type: String,
    default: ''
  }
})
</script>

<template>
  <img class="object-cover rounded-full"
    :style="{ width: size + 'px', height: size + 'px', borderRadius: type === 'square' ? '4px' : '50%' }"
    :src="url ? url : AvatarDefault" alt="">
</template>