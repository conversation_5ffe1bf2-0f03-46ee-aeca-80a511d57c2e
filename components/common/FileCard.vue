<script setup lang="ts">

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['onDelete'])

const confirm = useConfirm()

const downloadFile = () => {
  useDownload(props.data)
}

const deleteFile = () => {
  emit('onDelete', props.data)
  // confirm.require({
  //   message: 'Are you sure you want to delete?',
  //   header: 'Confirmation',
  //   rejectLabel: 'Cancel',
  //   acceptLabel: 'Delete',
  //   acceptClass: 'p-button-danger',
  //   rejectClass: 'p-button-help',
  //   accept: async () => {

  //   toast.add({ severity: 'success', summary: 'Notifications', detail: 'Successfully', life: 3000 })
  //   },
  //   reject: () => {
  //     // toast.add({ severity: 'error', summary: 'Rejected', detail: 'You have rejected', life: 3000 })
  //   }
  // })
}

</script>

<template>
  <div class="py-[12px] flex items-center gap-3 border-b-1 border-black-20 border-solid">
    <div class="w-[40px] h-[40px] flex items-center justify-center bg-primary-10 rounded">
      <img class="icon" src="~/assets/icons/i-doc-primary.svg" alt="">
    </div>

    <div class="flex-1">
      <p class="text-base font-normal c-black-90 line-clamp-1 cursor-pointer" @click="downloadFile">
        {{ data?.file_name }}
      </p>
      <p class="text-xs font-normal c-black-60">
        {{ useConvertSizeFile(data?.size) }}
      </p>
    </div>

    <div class="flex gap-2">
      <button @click="downloadFile">
        <img class="icon-lg" src="~/assets/icons/i-download-circle.svg" alt="">
      </button>
      <button @click="deleteFile">
        <img class="icon-lg" src="~/assets/icons/i-trash-circle.svg" alt="">
      </button>
    </div>
  </div>
</template>