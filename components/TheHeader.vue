<script setup lang="ts">
import { useAppStore } from '~/stores/app'
import Notification from './Notification.vue'

const { $auth } = useNuxtApp()
const router = useRouter()
const userPanel = ref()
const appStore = useAppStore()
const userInfo = computed(() => appStore.userInfo)
const listCart = computed(() => appStore.listCart)
const toggleUserPanel = (e: Event) => {
  userPanel.value.toggle(e)
}

const hideUserPanel = () => {
  userPanel.value.hide()
}

const goProfile = () => {
  hideUserPanel()
}
const listMenu = ref([
  {
    name: 'Bảng giá',
    url: '/pricing',
  },
  {
    name: 'T<PERSON>i liệu bán hàng',
    url: '/docs',
  },
  // {
  //   name: 'Thông báo',
  //   url: '/notification',
  // },
  // {
  //   name: '<PERSON><PERSON><PERSON><PERSON><PERSON> mãi',
  //   url: '/promotion',
  // },
  {
    name: '<PERSON> tức',
    url: '/news',
  },
  // {
  //   name: 'Hỗ trợ',
  //   url: '/support',
  // },
])

const logout = () => {
  hideUserPanel()
  $auth.logout().then(() => {
    appStore.listCart = []
    appStore.listOrder = []
    router.push('/login')
    // window.location.href = `${useRuntimeConfig().public.urlLogin}/logout/?back=${window.location.origin}/`
  })
}
</script>

<template>
  <div class="fr jc-sb ai-c py-2 px-4 bg-white border-b-1 border-[#EFEFEF] border-solid z-99">
    <div class="fr ai-c ml-10 lg:ml-4 gap-6 menu">
      <nuxt-link
        :to="item.url"
        class="text-base font-semibold c-black hover:c-primary transition-all hidden lg:block"
        v-for="item in listMenu">
        {{ item.name }}
      </nuxt-link>
    </div>
    <div class="sticky top-0 flex items-center justify-end gap-4">
      <nuxt-link to="/cart" class="relative mr-4">
        <div
          class="absolute px-[6px] py-[2px] bg-red text-white text-xs rounded-full top-[-9px] right-[-8px]"
          v-if="listCart.length > 0">
          {{ listCart.length }}
        </div>
        <img src="@/assets/icons/i-cart.svg" class="icon" alt="" />
      </nuxt-link>
      <div class="fc jc-c">
        <div class="text-base c-black font-semibold max-w-[250px] truncate">{{ userInfo?.FullName }}</div>
        <div class="text-xs c-primary font-normal">Số điểm còn lại: {{ useFormatNumber(userInfo?.RewardPoint) }}</div>
      </div>
      <BaseAvatar class="cursor-pointer" :size="40" :url="$auth?.user?.avatar" @click="toggleUserPanel" />

      <OverlayPanel class="min-w-[320px] [&>.p-overlaypanel-content]:p-0" ref="userPanel">
        <nuxt-link
          class="py-[12px] px-2 w-full flex items-center gap-3 bg-white transition-all hover:bg-gray-20 !border-none"
          to="/profile"
          @click="goProfile">
          <img src="~/assets/icons/i-user.svg" alt="" />
          <span class="text-base font-normal c-black"> Thông tin tài khoản </span>
        </nuxt-link>
        <button
          class="py-[12px] px-2 w-full flex items-center gap-3 bg-white transition-all hover:bg-gray-20"
          @click="logout">
          <img src="~/assets/icons/i-logout.svg" alt="" />
          <span class="text-base font-normal c-danger"> Đăng xuất </span>
        </button>
      </OverlayPanel>
    </div>
  </div>
</template>

<style scoped>
.menu a.router-link-active.router-link-exact-active {
  color: #004c82;
  font-weight: 800 !important;
  border-bottom: 2px solid #004c82 !important;
}
</style>
