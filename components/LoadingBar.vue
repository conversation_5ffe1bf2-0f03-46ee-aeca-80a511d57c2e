<script setup lang="ts">
import { useAppStore } from '~/stores/app'

const nuxtApp = useNuxtApp()
const appStore = useAppStore()

const isLoading = computed(() => appStore.isLoading)
</script>

<template>
  <div class="loading-page" v-if="isLoading">
    <div class="kids-loading"></div>
  </div>
</template>

<style lang="scss" scoped>
.loading-page {
  position: fixed;
  z-index: 100;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(#fff, 0.6);
  text-align: center;
  font-size: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: sans-serif;
}
.loading-page img {
  width: 150px;
}
.kids-loading {
  position: relative;
  width: 140px;
  height: 140px;
  margin: auto;
  background-position: center center;
  background-repeat: no-repeat;
  background-color: transparent !important;
  background-image: url(./loading.svg) !important;
  -webkit-transform: scale(0.5);
  -moz-transform: scale(0.5);
  -ms-transform: scale(0.5);
  -o-transform: scale(0.5);
  transform: scale(0.5);
}
.kids-loading:before {
  content: '';
  position: absolute;
  display: block;
  width: 180px;
  height: 180px;
  top: -25px;
  left: -30px;
  border-radius: 100px;
  box-shadow: 0 6px 0 0 #00669f;
  -ms-animation: kids-loading-anim 1s linear infinite;
  -moz-animation: kids-loading-anim 1s linear infinite;
  -webkit-animation: kids-loading-anim 1s linear infinite;
  -o-animation: kids-loading-anim 1s linear infinite;
  animation: kids-loading-anim 1s linear infinite;
}
@-webkit-keyframes kids-loading-anim {
  0% {
    -ms-transform: rotate(0);
    -moz-transform: rotate(0);
    -webkit-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes kids-loading-anim {
  0% {
    -ms-transform: rotate(0);
    -moz-transform: rotate(0);
    -webkit-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-moz-keyframes kids-loading-anim {
  0% {
    -ms-transform: rotate(0);
    -moz-transform: rotate(0);
    -webkit-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-ms-keyframes kids-loading-anim {
  0% {
    -ms-transform: rotate(0);
    -moz-transform: rotate(0);
    -webkit-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-moz-keyframes kids-loading-anim {
  0% {
    -ms-transform: rotate(0);
    -moz-transform: rotate(0);
    -webkit-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes kids-loading-anim {
  0% {
    -ms-transform: rotate(0);
    -moz-transform: rotate(0);
    -webkit-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-o-keyframes kids-loading-anim {
  0% {
    -ms-transform: rotate(0);
    -moz-transform: rotate(0);
    -webkit-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes kids-loading-anim {
  0% {
    -ms-transform: rotate(0);
    -moz-transform: rotate(0);
    -webkit-transform: rotate(0);
    -o-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
</style>
