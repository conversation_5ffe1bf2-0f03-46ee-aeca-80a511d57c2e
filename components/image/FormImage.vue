<script lang="ts" setup>
import { useForm } from 'vee-validate'
const form = defineModel<any>('form')
const props = defineProps({
  isLoading: {
    type: Boolean,
    required: false,
  },
})

const { handleSubmit } = useForm()

const emits = defineEmits(['submit', 'cancel', 'edit'])
const isEdit = computed(() => form.value.id)
const onSubmit = handleSubmit(() => {
  isEdit.value ? emits('edit', { ...toRaw(form.value) }) : emits('submit', { ...toRaw(form.value) })
})
const uploadImage = (file: any) => {
  console.log(file, 'file')
  form.value.images = [...form.value.images, ...file]
}
const removeImage = (index: number) => {
  form.value.images.splice(index, 1)
}
watch(
  () => form.value,
  (newValue) => {
    if (!newValue.images) {
      form.value.images = []
    }
  },
  { deep: true, immediate: true },
)
</script>

<template>
  <form class="grid grid-cols-1 lg:grid-cols-2 gap-4 p-4" @submit.prevent="onSubmit">
    <BaseInputText
      class="flex-1 mb-3"
      name="materialNumber"
      label="Material Number"
      :rules="{ required: true }"
      v-model="form.materialNumber" />
    <BaseInputText
      class="flex-1 mb-3"
      name="salesMatNumber"
      label="Sales Material Number"
      :rules="{ required: true }"
      v-model="form.salesMatNumber" />
    <div class="flex-1 col-span-2 flex flex-wrap relative gap-4">
      <div
        class="w-[125px] h-[160px] relative border-#cccccc border-1px border-solid rounded"
        v-for="(image, index) in form.images"
        :key="index">
        <img
          :src="image.url"
          :alt="form.materialNumber"
          class="w-full h-full object-cover rounded border"
          style="vertical-align: middle" />
        <div
          class="w-30px h-30px absolute top-0 right-0 cursor-pointer bg-white rounded-full fr ai-c jc-c"
          @click="removeImage(index)">
          <img src="@/assets/icons/i-trash-red.svg" class="absolute top-0 right-0 cursor-pointer" alt="" />
        </div>
      </div>
      <ButtonUpload accept="image" v-slot="slotProps" @onUpload="uploadImage">
        <div class="w-[125px] h-[160px] flex flex-col justify-center items-center bg-black-10 rounded">
          <img src="~/assets/icons/i-camera-bg-gray.svg" />
          <span class="text-base font-normal text-center c-black-60"> Hình ảnh </span>
        </div>
      </ButtonUpload>
    </div>
    <div class="col-span-2 fr jc-fe gap-4">
      <Button severity="cancel" @click="emits('cancel')">Hủy</Button>
      <Button severity="primary" type="submit" :loading="isLoading">{{ !isEdit ? 'Tạo' : 'Lưu' }}</Button>
    </div>
  </form>
</template>
