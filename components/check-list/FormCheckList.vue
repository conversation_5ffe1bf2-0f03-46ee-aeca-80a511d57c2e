<script setup lang="ts">
import { useAppStore } from 'stores/app'
import { useForm } from 'vee-validate'
import draggable from 'vuedraggable'

type DataType = {
  name: string
  check_items: string[]
  is_active: boolean
  _id: string
}

const props = defineProps({
  data: {
    type: Object as PropType<DataType>,
    default: () => ({})
  },
})

const emit = defineEmits(['onSubmit'])

const appStore = useAppStore()
const router = useRouter()
const { handleSubmit } = useForm()

const loading = computed(() => appStore.isRequesting)

const form = ref({
  name: props.data.name || '',
  check_items: props.data.check_items || [],
  is_active: true
})

const addCheckList = () => {
  form.value.check_items.push('')
}

const removeCheckListByIndex = (index: number) => {
  const newData = [...form.value.check_items]
  newData.splice(index, 1)

  form.value.check_items = newData
}

const onSubmit = handleSubmit(() => {
  emit('onSubmit', toRaw(form.value))
})

const goBack = () => {
  router.go(-1)
}

watch(() => props.data, (newValue) => {
  if (newValue) {
    form.value = {
      name: newValue.name || '',
      check_items: newValue.check_items || [],
      is_active: newValue.is_active
    }
  }
}, { deep: true })

</script>

<template>
  <form autocomplete="off" @submit.prevent="onSubmit">
    <BaseInputText class="mb-2" name="name" label="Name" :rules="{ required: true }" v-model="form.name" />

    <div class="mb-2 flex flex-col gap-2">
      <div class="flex items-center gap-4">
        <label class="text-base font-normal c-black-90">
          Check list <span class="c-danger">*</span>
        </label>
        <button type="button" @click="addCheckList">
          <img src="~/assets/icons/i-plus-circle-primary.svg" alt="">
        </button>
      </div>
    </div>
    <draggable class="flex flex-col gap-1" v-model="form.check_items" group="group" itemKey="id" draggable=".item">
      <template #item="{ item, index }">
        <div class="mb-2 flex items-center gap-4 item">
          <img src="@/assets/icons/i-move.svg" class="icon cursor-pointer" alt="">
          <BaseInputText class="flex-1" :name="`name-${index}`" v-model="form.check_items[index]" />
          <button type="button" @click="removeCheckListByIndex(index)">
            <img src="~/assets/icons/i-trash-circle.svg" alt="">
          </button>
        </div>
      </template>
    </draggable>

    <BaseSwitch label="Active" v-model="form.is_active" />

    <div class="flex justify-end gap-4">
      <Button label="Cancel" severity="cancel" type="button" @click="goBack" />
      <Button :label="props.data?._id ? 'Save' : 'Create'" severity="primary" type="submit"
        :disabled="form.name && form.check_items.length > 0 ? false : true" :loading="loading" />
    </div>
  </form>
</template>