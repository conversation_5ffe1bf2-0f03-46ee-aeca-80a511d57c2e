<script lang="ts" setup>
import { useForm } from 'vee-validate'
const form = defineModel<any>('form')
const props = defineProps({
  isLoading: {
    type: Boolean,
    required: false,
  },
})

const { handleSubmit } = useForm()

const emits = defineEmits(['submit', 'cancel', 'edit'])
const isEdit = computed(() => form.value.id)
const onSubmit = handleSubmit(() => {
  isEdit.value ? emits('edit', { ...toRaw(form.value) }) : emits('submit', { ...toRaw(form.value) })
})
const uploadImage = (file: any) => {
  console.log(file, 'file')
  form.value.thumbnailUrl = file[0].url
}
const removeImage = (index: number) => {
  form.value.images.splice(index, 1)
}
</script>

<template>
  <form class="grid grid-cols-1 lg:grid-cols-2 gap-4 p-4" @submit.prevent="onSubmit">
    <div class="flex-1 col-span-2 flex fc flex-wrap relative gap-4">
      <div class="text-base c-black-90">Hình <PERSON>nh thumbnail <span style="color: red">*</span></div>

      <ButtonUpload accept="image" v-slot="slotProps" @onUpload="uploadImage" :uploadType="'NEWS_CONTENT'">
        <div
          class="w-[125px] h-[160px] relative border-#cccccc border-1px border-solid rounded"
          v-if="form.thumbnailUrl">
          <img
            :src="form.thumbnailUrl"
            :alt="form.title"
            class="w-full h-full object-cover rounded border"
            style="vertical-align: middle" />
        </div>
        <div class="w-[125px] h-[160px] flex flex-col justify-center items-center bg-black-10 rounded" v-else>
          <img src="~/assets/icons/i-camera-bg-gray.svg" />
          <span class="text-base font-normal text-center c-black-60"> Hình ảnh </span>
        </div>
      </ButtonUpload>
    </div>
    <div class="flex-1 mb-3">
      <BaseInputText name="title" label="Tiêu đề" :rules="{ required: true }" v-model="form.title" />
    </div>
    <div class="flex-1 mb-3">
      <BaseInputSelect
        class="flex-1"
        v-model="form.objectType"
        name="type"
        placeholder="Chọn"
        :options="[
          {
            label: 'Khách hàng',
            value: 'CUSTOMER',
          },
          {
            label: 'Sale',
            value: 'SALES',
          },
        ]"
        option-label="label"
        label="Đối tượng"
        option-value="value"
        :rules="{ required: true }" />
    </div>
    <BaseEditor class="col-span-2" v-model="form.content" label="Nội dung" name="content" :rules="{ required: true }" />

    <div class="col-span-2 fr jc-fe gap-4">
      <Button severity="cancel" @click="emits('cancel')">Hủy</Button>
      <Button severity="primary" type="submit" :loading="isLoading">{{ !isEdit ? 'Tạo' : 'Lưu' }}</Button>
    </div>
  </form>
</template>
