<script setup lang="ts">
import {
  iCatalogue,
  iCatalogueActive,
  iDashboard,
  iDashboardActive,
  iEconomic,
  iEconomicActive,
  iGuarantee,
  iGuaranteeActive,
  iHistory,
  iHistoryActive,
  iImage,
  iImageActive,
  iInventory,
  iInventoryActive,
  iMenuCart,
  iMenuCartActive,
  iNew,
  iNewActive,
  iNotification,
  iNotificationActive,
  iOrder,
  iOrderActive,
  iPointConvert,
  iPointConvertActive,
  iPointPolicy,
  iPointPolicyActive,
  iPricing,
  iPricingActive,
  iPromotion,
  iPromotionActive,
  iSupport,
  iSupportActive,
  iUserGroup,
  iUserGroupActive,
} from '~/assets/icons'
import { useAppStore } from '~/stores/app'

type NavItem = {
  key: string
  title: string
  isShow: boolean
  menus?: any
}

type MenuItem = {
  name: string
  to?: string
  subMenu?: Object[]
  isCollapse?: boolean
}
const { $auth: auth }: any = useNuxtApp()
const appStore = useAppStore()
const userInfo = computed(() => appStore.userInfo)
const listCart = computed(() => appStore.listCart)
const router = useRouter()
const route = useRoute()
const { isAdmin, isCustomer } = useRole()

const navList = computed(() => {
  return isAdmin
    ? [
        {
          key: 'admin',
          title: 'Quản trị',
          isShow: true,
          menus: [
            {
              icon: iUserGroup,
              iconActive: iUserGroupActive,
              name: 'Người dùng',
              key: 'user',
              to: '/admin/user',
              isShow: true,
            },
            {
              icon: iNew,
              iconActive: iNewActive,
              name: 'Tin tức',
              key: 'news',
              to: '/admin/news',
              isShow: true,
            },
            {
              icon: iPricing,
              iconActive: iPricingActive,
              name: 'Bảng giá',
              key: 'pricing',
              to: '/admin/pricing',
              isShow: true,
            },
            {
              icon: iCatalogue,
              iconActive: iCatalogueActive,
              name: 'Catalogue',
              key: 'catalogue',
              to: '/admin/catalogue',
              isShow: true,
            },
            {
              icon: iImage,
              iconActive: iImageActive,
              name: 'Hình ảnh sản phẩm',
              key: 'image',
              to: '/admin/image',
              isShow: true,
            },
            {
              icon: iSupport,
              iconActive: iSupportActive,
              name: 'Quản lý yêu cầu',
              key: 'support',
              to: '/admin/support',
              isShow: true,
            },
          ],
        },
      ]
    : [
        {
          key: 'menu',
          title: 'Thông tin',
          isShow: true,
          menus: [
            {
              icon: iDashboard,
              iconActive: iDashboardActive,
              name: 'Thống kê',
              key: 'dashboard',
              to: '/dashboard',
              isShow: true,
            },
            {
              icon: iNotification,
              iconActive: iNotificationActive,
              name: 'Tin tức',
              key: 'news',
              to: '/news',
              isShow: true,
            },
            // {
            //   icon: iPromotion,
            //   iconActive: iPromotionActive,
            //   name: 'Khuyến mãi',
            //   key: 'promotion',
            //   to: '/promotion',
            //   isShow: true,
            // },
          ],
        },
        {
          key: 'menu',
          title: 'Đơn hàng',
          isShow: true,
          menus: [
            {
              icon: iOrder,
              iconActive: iOrderActive,
              name: 'Tạo đơn hàng',
              key: 'order',
              to: '/order',
              isShow: true,
            },
            {
              icon: iMenuCart,
              iconActive: iMenuCartActive,
              name: 'Giỏ hàng',
              key: 'cart',
              to: '/cart',
              isShow: true,
            },
            {
              icon: iHistory,
              iconActive: iHistoryActive,
              name: 'Danh sách đơn hàng',
              key: 'history',
              to: '/history',
              isShow: true,
            },
            {
              icon: iPointPolicy,
              iconActive: iPointPolicyActive,
              name: 'Chính sách điểm',
              key: 'point-policy',
              to: '/point-policy',
              isShow: true,
            },
            {
              icon: iInventory,
              iconActive: iInventoryActive,
              name: 'Kiểm tra tồn kho',
              key: 'inventory',
              to: '/inventory',
              isShow: !!isCustomer,
            },
            {
              icon: iEconomic,
              iconActive: iEconomicActive,
              name: 'Hạn mức công nợ',
              key: 'economic',
              to: '/economic',
              isShow: true,
            },
            {
              icon: iPointConvert,
              iconActive: iPointConvertActive,
              name: 'Đổi điểm tích lũy',
              key: 'convert-point',
              to: '/convert-point',
              isShow: true,
            },
            {
              icon: iHistory,
              iconActive: iHistoryActive,
              name: 'Lịch sử đổi điếm',
              key: 'history-point',
              to: '/history/point',
              isShow: true,
            },
            {
              icon: iGuarantee,
              iconActive: iGuaranteeActive,
              name: 'Tra cứu bảo hành',
              key: 'guarantee',
              to: '/tra-cuu-bao-hanh',
              isShow: true,
            },

            // {
            //   icon: iEmployee,
            //   iconActive: iEmployeeActive,
            //   name: 'Employees',
            //   key: 'view_admin',
            //   isShow: true,
            //   subMenu: [
            //     {
            //       name: 'Users',
            //       to: '/employees/users',
            //       key: 'view_admin',
            //       isShow: true,
            //     },
            //     {
            //       name: 'Roles',
            //       to: '/employees/roles',
            //       key: 'view_role',
            //       isShow: true,
            //     },
            //   ],
            // },
          ],
        },
      ]
})

const isExpand = ref(true)
const isShow = ref(false)

const toggleExpand = () => {
  isExpand.value = !isExpand.value
}

const toggleShow = () => {
  isShow.value = !isShow.value
}

const mapNavList = () => {
  // if (auth.user?.is_admin) {
  // } else {
  //   const cloneNavList = JSON.parse(JSON.stringify(navList.value))
  //   cloneNavList.map((lv1: any) => {
  //     lv1.menus.map((lv2: any) => {
  //       lv2.isShow = auth.user.permissions.includes(lv2.key)
  //       lv2.menus?.map((lv3: any) => {
  //         lv3.isShow = auth.user.permissions.includes(lv3.key)
  //       })
  //       return lv2
  //     })
  //     return lv1
  //   })
  //   navList.value = cloneNavList
  //   console.log(cloneNavList, 'permissions')
  // }
}

mapNavList()
const handleClickMenu = (key: string, menu: MenuItem) => {
  if (menu?.subMenu) {
    const newNavList = JSON.parse(JSON.stringify(navList.value))
    const currentNav = newNavList.find((item: NavItem) => item?.key === key)
    currentNav?.menus.map((item: MenuItem) => {
      if (item.name === menu.name) {
        item.isCollapse = !item.isCollapse
      }
      return item
    })

    navList.value = newNavList
  }
}
</script>

<template>
  <div
    class="pt-4 min-w-[268px] w-[268px] h-screen flex flex-col gap-8 bg-primary transition-all fixed top-0 left-0 -translate-x-full [&.active]:translate-x-0 lg-relative lg-translate-x-0 z-102"
    :class="isShow ? ['active'] : ''"
    :style="!isExpand ? { minWidth: '76px', width: '76px' } : {}">
    <div class="text-center">
      <nuxt-link to="/">
        <img class="w-70% object-cover" src="~/assets/images/logo-white.svg" alt="" v-if="isExpand" />
        <img class="h-[40px] object-cover" src="~/assets/images/logo-short.svg" alt="" v-else />
      </nuxt-link>
    </div>

    <!-- <nuxt-link to="/profile" class="w-full px-2" v-if="isExpand">
      <div class="w-full fr items-center bg-white p-2 rounded">
        <BaseAvatar class="cursor-pointer min-w-40px" :size="40" :url="auth?.user?.avatarUrl" />
        <div class="fc jc-c ml-4">
          <div class="text-base c-black font-semibold max-w-[180px] truncate">{{ userInfo?.FullName }}</div>
          <div class="text-xs c-primary font-normal">Số điểm còn lại: {{ useFormatNumber(userInfo?.RewardPoint) }}</div>
        </div>
      </div>
    </nuxt-link> -->

    <!-- Nav -->
    <nav class="flex-1 overflow-auto">
      <div v-for="nav in navList" :key="nav.key">
        <h3
          class="text-base font-normal c-black-50 transition-all px-2 mb-2"
          :style="isExpand ? {} : { textAlign: 'center' }"
          v-if="nav.isShow"
          v-show="isExpand">
          {{ nav.title }}
        </h3>
        <!-- Menu -->
        <ul
          class="menu mx-a"
          :style="!isExpand ? { display: 'flex', 'flex-direction': 'column', 'justify-content': 'center' } : {}"
          v-if="nav.isShow">
          <li
            class="menu-item mb-0.5"
            v-for="menu in nav.menus"
            :key="menu.name"
            @click.stop="handleClickMenu(nav.key, menu)">
            <nuxt-link
              v-if="menu.isShow"
              class="menu-item__link p-[12px] flex items-center gap-2 cursor-pointer transition-all hover:bg-gray-6-5"
              :style="isExpand ? {} : { justifyContent: 'center' }"
              :to="menu.to">
              <img class="icon" :src="menu.icon" alt="" />
              <img class="icon icon-active" :src="menu.iconActive" alt="" />
              <span class="flex-1 text-base c-white" v-show="isExpand">
                {{ menu.name }}
              </span>
              <!-- <img
                class="icon icon-angle transition-all"
                :class="{ 'rotate-180': !menu.isCollapse }"
                src="~/assets/icons/i-angle-up-white.svg"
                alt=""
                v-if="menu.subMenu && isExpand" /> -->
            </nuxt-link>

            <!-- Sub menu -->
            <ul class="sub-menu" v-show="menu.isCollapse" v-if="menu.subMenu && menu.subMenu.length > 0">
              <li
                class="mb-0.5"
                v-for="child in menu.subMenu"
                :key="child.name"
                @click.stop="handleClickMenu(nav.key, child)">
                <nuxt-link
                  v-if="child.isShow"
                  class="p-[12px] flex items-center gap-2 rounded cursor-pointer transition-all hover:bg-gray-6-5"
                  :style="isExpand ? {} : { justifyContent: 'center' }"
                  :to="child.to">
                  <img class="icon" src="~/assets/icons/i-line-black.svg" alt="" />
                  <img class="icon icon-active" src="~/assets/icons/i-line-primary.svg" alt="" />
                  <span class="flex-1 text-base c-white" v-if="isExpand">
                    {{ child.name }}
                  </span>
                </nuxt-link>
              </li>
            </ul>
          </li>
        </ul>
      </div>
    </nav>

    <div
      class="absolute top-[90vh] -right-12 cursor-pointer z-100 hidden lg-block"
      :class="!isExpand ? ['rotate-180'] : ''"
      @click="toggleExpand">
      <img src="~/assets/icons/i-collapse-blue.svg" alt="" />
    </div>
  </div>

  <!-- Mobile -->
  <div
    class="fixed top-18 left-16 cursor-pointer z-101 lg:hidden"
    :class="isShow ? ['left-252'] : ''"
    @click="toggleShow">
    <img class="icon" src="~/assets/icons/i-menu-bar.svg" alt="" />
  </div>

  <div class="fixed inset-0 bg-black/20 z-101 hidden" :class="isShow ? ['!block'] : ''" @click="toggleShow"></div>
</template>

<style lang="scss" scoped>
.menu {
  .icon-active {
    display: none;
  }

  .menu-item {
    &:has(.sub-menu li a.router-link-exact-active),
    .router-link-exact-active,
    .router-link-active {
      &.menu-item__link,
      .menu-item__link {
        background: hsla(0, 0%, 100%, 0.08);

        span {
          color: #ffffff;
        }

        .icon {
          display: none;
        }

        .icon-active {
          display: block;
        }

        .icon-angle {
          display: block;
          filter: brightness(0) invert(1);
        }
      }
    }
  }

  .sub-menu {
    margin-top: 6px;

    .router-link-exact-active,
    .router-link-active {
      background-color: #e0efff;

      span {
        color: #004c82;
      }

      .icon {
        display: none;
      }

      .icon-active {
        display: block;
      }
    }
  }
}
</style>
