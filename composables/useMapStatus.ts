export default function useMapStatus() {
  const statusPayment: any = (status: string) => {
    switch (status) {
      case 'paid':
        return 'Paid'
      case 'failed':
        return 'Failed'
      case 'pending':
        return 'Pending'
      case 'expired':
        return 'Expired'
      case 'active':
        return 'Active'
      case 'canceled':
        return 'Canceled'
      case 'success':
        return 'Success'
      default:
        return 'Pending'
    }
  }
  return {
    statusPayment,
  }
}
