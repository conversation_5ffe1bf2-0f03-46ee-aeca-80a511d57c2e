export default function useDownload(obj: any) {
  const { $auth: auth } = useNuxtApp()
  //@ts-ignore
  const token = auth.strategy?.token.get()
  //@ts-ignore
  const url = useRuntimeConfig().public.apiBase + `/media/admin-access?key=${obj.key}&token=${token}`
  const link = document.createElement('a')
  link.href = url
  link.download = obj.name
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}
