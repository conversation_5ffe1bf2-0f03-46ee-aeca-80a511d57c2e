export const useRole = () => {
  const { $auth: auth }: any = useNuxtApp()
  const isCustomer = computed(() => {
    if (auth.user?.roles && (auth.user?.roles[0] === 'ROLE_CUSTOMER' || auth.user?.roles[0] === 'ROLE_CUSTOMER_ORDER'))
      return true
    else return false
  })
  const userType = computed(() => {
    if (auth.user?.roles && (auth.user?.roles[0] === 'ROLE_CUSTOMER' || auth.user?.roles[0] === 'ROLE_CUSTOMER_ORDER'))
      return 'CUSTOMER'
    else return 'SALES'
  })
  const isAdmin = computed(() => {
    if (auth.user?.roles && auth.user?.roles[0] === 'ROLE_IT_ADMIN') return true
    else return false
  })
  return {
    isCustomer: unref(isCustomer),
    userType: unref(userType),
    isAdmin: unref(isAdmin),
  }
}
