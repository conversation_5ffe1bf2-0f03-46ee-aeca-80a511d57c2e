export const useInfo = () => {
  const isFirst = ref(true)
  const { $auth: auth }: any = useNuxtApp()
  const { isCustomer } = useRole()
  const userInfo = ref<any>({})
  console.log('info')
  const getData = async () => {
    if (isFirst.value == true) {
      isFirst.value = false

      const url = `sap_api_get?sap_api_url=ZALP_SALES_ORDER_SRV/PortalUserSet('${
        isCustomer ? auth?.user?.sapCustomerId : auth?.user?.username
      }')?$format=json`
      const { data }: any = await useApi(url, {
        method: 'GET',
      })
      userInfo.value = data.value?.d || {}
      console.log(data.value?.d)
    }
  }
  if (isFirst.value == true) {
    getData()
  }
  // watch(
  //   () => auth?.loggedIn,
  //   () => {
  //     isFirst.value = true
  //   },
  // )
  return {
    userInfo: unref(userInfo),
  }
}
