import { useAppStore } from '~/stores/app'

export default defineNuxtRouteMiddleware((to, from) => {
  const { $auth: auth } = useNuxtApp()
  const appStore = useAppStore()
  const userInfo = computed(() => appStore.userInfo)
  if (!auth?.loggedIn) {
    appStore.isGetInfo = false
    appStore.userInfo = {}
    appStore.listImage = []
    appStore.listMaterial = []
    appStore.listMaterialSearch = []
    appStore.listCustomer = []
  }
  if (auth?.loggedIn && appStore.isGetInfo == false && !userInfo.value?.Username) {
    appStore.isGetInfo = true
  }
  if (appStore.isGetInfo) {
    appStore.isGetInfo = false
    const { isCustomer } = useRole()
    const getUserInfo = async () => {
      const url = `sap_api_get?sap_api_url=ZALP_SALES_ORDER_SRV/PortalUserSet('${
        isCustomer ? auth?.user?.sapCustomerId : auth?.user?.username
      }')?$format=json`
      const { data }: any = await useApi(url, {
        method: 'GET',
      })
      appStore.userInfo = data.value?.d
    }
    const getListImage = async () => {
      const url = `product-image/list`
      const { data }: any = await useApi(url, {
        method: 'GET',
      })
      appStore.listImage = data.value?.data || []
    }
    const getListMaterialSearch = async () => {
      const url = encodeURIComponent(`material:list`)
      const { data }: any = await useApi(url, {
        method: 'GET',
      })
      appStore.listMaterialSearch =
        data.value?.data.map((item: any) => ({ ...item, label: item.materialNumber + ' - ' + item.name })) || []
    }
    const getListMaterial = async () => {
      const url = encodeURIComponent(`material:promoteList`)
      const { data }: any = await useApi(url, {
        method: 'GET',
        params: {
          sap_api_url: `ZALP_SALES_ORDER_SRV/PromoMatSet?$format=json`,
        },
      })
      appStore.listMaterial =
        data.value?.d?.results.reduce((acc: any, cur: any) => {
          const index = acc.findIndex((el: any) => el.Salesmatnumber === cur.Salesmatnumber)
          if (index !== -1) {
            if (cur.Variationtype === 'S') {
              acc[index].Variationvalue = [...acc[index].Variationvalue, cur.Variationvalue]
            }
            if (cur.Variationtype === 'C') {
              acc[index].Colorcode = [...acc[index].Colorcode, cur.Colorcode]
            }
            return acc
          } else {
            if (cur.Variationtype === 'S') {
              cur.Variationvalue = [cur.Variationvalue]
            }
            if (cur.Variationtype === 'C') {
              cur.Colorcode = [cur.Colorcode]
            }
            return [...acc, cur]
          }
        }, []) || []
    }
    const getCustomer = async () => {
      const url = `ZALP_SALES_ORDER_SRV/PortalUserSet('${auth?.user?.username}')?$expand=toCustomers&$format=json`
      const { data }: any = await useApi(`sap_api_get`, {
        method: 'GET',
        params: {
          sap_api_url: url,
        },
      })
      appStore.listCustomer = data.value?.d?.toCustomers?.results || []
    }
    if (!isCustomer) {
      getCustomer()
    }
    getUserInfo()
    getListImage()
    getListMaterial()
    getListMaterialSearch()
  }
})
