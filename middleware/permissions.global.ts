export default defineNuxtRouteMiddleware((to, from) => {
  const { $auth } = useNuxtApp()
  if (!$auth?.loggedIn && !to.meta?.auth) {
    navigateTo('/')
  }
  if ($auth.loggedIn) {
    // const pathPublic = ['home', 'profile']
    // const firstSlashPath = to?.fullPath?.split('/')[1]
    // if (pathPublic?.includes(firstSlashPath)) {
    //   return
    // } else {
    //   // Account admin
    //   if ($auth?.user?.is_admin) return
    //   const userPermissions = $auth?.user?.permissions as string[]
    //   if (to?.meta?.permission) {
    //     if (userPermissions?.includes(to?.meta?.permission as string)) {
    //       return
    //     } else {
    //       return navigateTo('/')
    //     }
    //   }
    //   return
    //   // Account scope teacher & student
    //   // Not permission to redirect home
    // }
  }
})
