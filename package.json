{"name": "nuxt-app", "private": true, "scripts": {"build": "nuxt build", "build:prod": "nuxt build --dotenv .env.prod", "dev": "nuxt dev", "generate": "nuxt generate", "generate:prod": "nuxt generate --dotenv .env.prod", "preview": "nuxt preview", "postinstall": "nuxt prepare", "stag": "NODE_ENV=staging nuxt build && nuxt start"}, "devDependencies": {"@nuxt/devtools": "latest", "@types/node": "^20", "@vueuse/core": "^10.2.1", "@vueuse/nuxt": "^10.2.1", "nuxt": "^3.13.0", "sass": "^1.63.6"}, "dependencies": {"@ckeditor/ckeditor5-vue": "^5.1.0", "@nuxt-alt/auth": "^3.1.7", "@nuxt-alt/http": "^1.7.10", "@pinia/nuxt": "^0.4.11", "@unocss/nuxt": "^0.53.4", "apexcharts": "^4.7.0", "chart.js": "^4.3.3", "ckeditor5-custom-build": "file:./ckeditor5", "dayjs-nuxt": "^2.1.11", "jspdf": "^3.0.1", "moment": "^2.29.4", "pdf-vue3": "^1.0.12", "pinia": "^2.1.4", "primevue": "3.29.2", "progressbar.js": "^1.1.0", "vee-validate": "^4.10.7", "vue-css-donut-chart": "^2.1.0", "vue-json-excel3": "^1.0.30", "vue3-apexcharts": "^1.8.0", "vue3-observe-visibility": "^1.0.2", "vue3-otp-input": "^0.4.1", "vuedraggable": "^4.1.0"}, "overrides": {"vue": "latest"}}