import { fileURLToPath } from 'url'

//@ts-ignore
const configByEnv = process.env

console.log(configByEnv?.API_URL)

export default defineNuxtConfig({
  ssr: false,
  devtools: { enabled: true },
  app: {
    head: {
      link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.svg' }],
      title: 'An Lập Phát Portal',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1,maximum-scale=1,user-scalable=0' },
        {
          name: 'description',
          content: 'An Lập Phát Portal',
        },
        { property: 'og:image', content: '/favicon.svg' },
        { property: 'og:title', content: 'An Lập Phát Portal' },
        {
          property: 'og:description',
          content: 'An Lập Phát Portal',
        },
      ],
    },
    // pageTransition: { name: 'page', mode: 'out-in' },
  },

  runtimeConfig: {
    public: {
      apiBase: configByEnv.API_URL,
    },
  },
  alias: {
    components: fileURLToPath(new URL('./components', import.meta.url)),
    types: fileURLToPath(new URL('./types', import.meta.url)),
    stores: fileURLToPath(new URL('./stores', import.meta.url)),
    constants: fileURLToPath(new URL('./constants', import.meta.url)),
  },
  components: [
    {
      path: '~/components/common',
      pathPrefix: false,
    },
  ],
  modules: ['@nuxt-alt/auth', '@pinia/nuxt', '@unocss/nuxt', '@vueuse/nuxt', 'dayjs-nuxt'],
  css: [
    '~/assets/styles/reset.css',
    '~/assets/styles/prime-lara.css',
    'primevue/resources/primevue.css',
    '~/assets/styles/ck-content-editor.css',
    '~/assets/styles/styles.scss',
  ],
  build: {
    transpile: ['primevue'],
  },
  // spaLoadingTemplate: false,
  auth: {
    globalMiddleware: true,
    redirect: {
      login: '/login',
      logout: '/login',
      callback: '/login',
      home: '/',
    },
    strategies: {
      user: {
        scheme: 'local',
        endpoints: {
          login: {
            url: `${configByEnv.API_URL}/auth/login`,
          },
          logout: false,
          refresh: false,
          user: {
            url: `${configByEnv.API_URL}/auth/profile`,
          },
        },
        token: {
          property: 'data.token',
          type: 'Bearer',
          maxAge: 60 * 60 * 24 * 30,
        },
        // refreshToken: {
        //   property: 'data.refresh_token',
        //   data: 'refresh_token',
        //   maxAge: 60 * 60 * 2,
        // },
        user: {
          property: 'data',
        },
      },
    },
  },
})
