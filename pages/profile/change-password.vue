<script setup lang="ts">
import { useForm } from 'vee-validate'

const { $auth } = useNuxtApp()
const toast = useToast()

const { handleSubmit } = useForm()
const form = ref({
  name: $auth.user?.name || '',
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})
const isChangePassword = ref<boolean>(false)

const updateAccountProfile = handleSubmit(async () => {
  const { data, error } = await useApi('update-profile', {
    method: 'PUT',
    body: {
      name: form.value.name,
      password: form.value.newPassword,
    },
  })

  if (!error.value) {
    form.value.oldPassword = ''
    form.value.newPassword = ''
    form.value.confirmPassword = ''

    toast.add({ severity: 'success', summary: 'Notification', detail: 'Updated', life: 3000 })
  }
})
</script>

<template>
  <div class="page">
    <div class="box">
      <form autocomplete="off" @submit.prevent="updateAccountProfile">
        <h2 class="mb-3 text-xl font-bold c-black-90">Change password</h2>
        <BaseInputPassword
          class="mb-2"
          name="old-password"
          label="Old password"
          :rules="{ required: isChangePassword }"
          v-model="form.oldPassword" />
        <BaseInputPassword
          class="mb-2"
          name="new-password"
          label="New password"
          :rules="{ required: isChangePassword }"
          v-model="form.newPassword" />
        <BaseInputPassword
          class="mb-2"
          name="confirm-password"
          label="Confirm password"
          :rules="{ required: isChangePassword, confirmed: 'new-password' }"
          v-model="form.confirmPassword" />

        <div class="flex justify-end gap-4">
          <Button label="Save" severity="primary" type="submit" />
        </div>
      </form>
    </div>
  </div>
</template>
