<script setup lang="ts">
import { useForm } from 'vee-validate'

const { $auth } = useNuxtApp()
const toast = useToast()

const { handleSubmit } = useForm()
const form = ref({
  name: $auth.user?.name || '',
  avatar: $auth.user?.avatar || '',
  phone: $auth.user?.phone || null,
})

const changeLogo = (file: Media) => {
  form.value.avatar = file.url
}

const updateAccountProfile = handleSubmit(async () => {
  const { data, error } = await useApi('update-profile', {
    method: 'PUT',
    body: {
      name: form.value.name,
      avatar: form.value.avatar,
      phone: form.value.phone,
    },
  })

  if (!error.value) {
    const { result } = await $auth.fetchUser()

    toast.add({ severity: 'success', summary: 'Notification', detail: 'Updated', life: 3000 })
  }
})
</script>

<template>
  <div class="container mx-a">
    <div class="box">
      <form autocomplete="off" @submit.prevent="updateAccountProfile">
        <div class="mb-3 mx-auto relative w-[80px] h-[80px] bg-black-50 rounded-full overflow-hidden">
          <div class="absolute inset-0" v-if="form.avatar">
            <img class="w-full h-full object-cover object-center rounded-full" :src="form.avatar" alt="" />
          </div>

          <ButtonUpload accept="image" v-slot="slotProps" @onUpload="changeLogo">
            <button
              class="p-[2px] absolute bottom-0 left-0 right-0 text-center bg-black-30 opacity-85"
              type="button"
              @click="slotProps.onClick">
              <img src="~/assets/icons/i-camera.svg" alt="" />
            </button>
          </ButtonUpload>
        </div>

        <div class="mb-3 flex gap-4">
          <BaseInputText class="flex-1" name="name" label="Name" :rules="{ required: true }" v-model="form.name" />
          <BaseInputNumber class="flex-1" name="phone" label="Phone" v-model="form.phone" />
        </div>

        <div class="flex justify-end gap-4">
          <Button label="Save" severity="primary" type="submit" />
        </div>
      </form>
    </div>
  </div>
</template>
