<script setup lang="ts">
import { useForm } from 'vee-validate'
import { useAppStore } from '~/stores/app'

const navItems = [
  { id: 'personal', name: 'Thông tin cá nhân', icon: 'UserIcon' },
  { id: 'security', name: '<PERSON><PERSON><PERSON> mật', icon: 'LockClosedIcon' },
]

// Reactive state
const activeTab = ref('personal')
const { $auth: auth }: any = useNuxtApp()
const { isCustomer } = useRole()
const isLoading = ref(false)
const { handleSubmit } = useForm()
const toast = useToast()
const form = ref<any>({})
const config = useRuntimeConfig()
const confirm = useConfirm()

const userInfo = computed(() => useAppStore().userInfo)
const onUpload = async (obj: any) => {
  try {
    const file = obj.target.files[0]
    const formData = new FormData()

    formData.append('file', file)
    await fetch(config.public.apiBase + `/user:uploadAvatar?userId=${auth.user.id}`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${auth.strategy?.token.get()}`,
      },
      body: formData,
    })
      .then((response) => response.json())
      .then((data) => {
        console.log(data, 'data')
        if (data?.data?.avatarUrl) {
          auth.fetchUser()
        }
      })
  } catch (error) {}
}
const changePassword = handleSubmit(async () => {
  isLoading.value = true
  const { data, error } = await useApi('customer_update_profile', {
    method: 'PUT',
    body: {
      currentPassword: form.value.oldPassword,
      newPassword: form.value.newPassword,
      address: auth.user?.address,
      phone: auth.user?.phone,
      email: auth.user?.email,
      firstName: auth.user?.firstName,
      lastName: auth.user?.lastName,
      language: auth.user?.language,
    },
  })

  isLoading.value = false
  if (error.value) {
    toast.add({ severity: 'error', summary: 'Thông báo', detail: 'Cập nhật thất bại', life: 3000 })
  } else {
    toast.add({ severity: 'success', summary: 'Thông báo', detail: 'Cập nhật thành công', life: 3000 })
  }
})

const confirmLogout = () => {
  confirm.require({
    message: 'Bạn có chắc chắn muốn đăng xuất?',
    header: 'Xác nhận',
    rejectLabel: 'Hủy',
    acceptLabel: 'Đăng xuất',
    acceptClass: 'p-button-danger',
    rejectClass: 'p-button-help',
    icon: 'pi pi-exclamation-triangle',
    accept: () => {
      auth.logout()
    },
    reject: () => {},
  })
}

watch(
  () => userInfo.value,
  () => {
    form.value = { ...userInfo.value }
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <div class="">
    <div class="container mx-a">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Thông tin tài khoản</h1>
        <p class="text-gray-600 mt-2">Quản lý thông tin cá nhân và cài đặt tài khoản của bạn</p>
      </div>

      <!-- Main Content -->
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Left Sidebar - Navigation -->
        <div class="lg:col-span-1 fc">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden min-h-fit">
            <!-- Navigation Menu -->
            <nav class="p-2">
              <button
                v-for="(item, index) in navItems"
                :key="index"
                @click="activeTab = item.id"
                class="w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors"
                :class="activeTab === item.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-50'">
                <span class="w-5 h-5">
                  <component :is="item.icon" class="w-5 h-5" />
                </span>
                <span>{{ item.name }}</span>
              </button>
              <button
                class="w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors c-red hover:bg-gray-50"
                @click="confirmLogout">
                <span class="w-5 h-5"> </span>
                <span> Đăng xuất </span>
              </button>
            </nav>
          </div>

          <!-- Order Stats Card -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mt-6 h-full">
            <div class="p-4">
              <h3 class="com-heading mb-4">Thống kê điểm</h3>
              <div class="space-y-4">
                <div class="flex justify-between items-center">
                  <span class="text-gray-600">Tổng số điểm thưởng</span>
                  <span class="font-medium text-gray-900">{{ useFormatNumber(userInfo?.GivenPoint) }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-600"> Tổng số điểm tích lũy</span>
                  <span class="font-medium text-blue-600">{{ useFormatNumber(userInfo?.AccumPoint) }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-600"> Tổng điểm đã sử dụng </span>
                  <span class="font-medium text-green-600">{{ useFormatNumber(userInfo?.UsedPoint) }}</span>
                </div>
                <div class="w-full h-1px bg-#ccc"></div>
                <div class="pt-4 border-t border-gray-200">
                  <div class="flex justify-between items-center">
                    <span class="text-gray-600">Tổng hiện tại</span>
                    <span class="font-medium text-gray-900">{{ useFormatNumber(userInfo?.RemainPoint) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Content Area -->
        <div class="lg:col-span-3 h-full">
          <!-- Personal Information Tab -->
          <div
            v-if="activeTab === 'personal'"
            class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden p-4 h-full">
            <div class="com-heading mb-4">Thông tin tài khoản</div>
            <div class="fr jc-c ai-c">
              <label for="uploadAvatar" class="cursor-pointer">
                <BaseAvatar :url="auth.user.avatarUrl" :size="145" aspect="126/160" v-if="auth.user.avatarUrl" />
                <div class="w-[125px] h-[160px] flex flex-col justify-center items-center bg-black-10 rounded" v-else>
                  <img src="~/assets/icons/i-camera-bg-gray.svg" />
                  <span class="text-base font-normal text-center c-black-60"> Avatar </span>
                </div>
                <input
                  type="file"
                  name="uploadAvatar"
                  id="uploadAvatar"
                  accept="image/*"
                  class="hidden"
                  @change="onUpload" />
              </label>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6 pb-20">
              <div class="fc gap-1 border-1px border-solid border-#cccccc p-2 rounded">
                <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Loại tài khoản:</div>
                <div class="text-base c-black font-semibold">
                  {{ isCustomer ? 'Tài khoản khách hàng' : 'Tài khoản nhân viên' }}
                </div>
              </div>
              <div class="fc gap-1 border-1px border-solid border-#cccccc p-2 rounded">
                <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">
                  {{ isCustomer ? 'Mã khách hàng' : 'Mã nhân viên' }}:
                </div>
                <div class="text-base c-black font-semibold">{{ userInfo?.Username }}</div>
              </div>
              <div class="fc gap-1 border-1px border-solid border-#cccccc p-2 rounded">
                <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">
                  {{ isCustomer ? 'Tên khách hàng:' : 'Tên nhân viên:' }}
                </div>
                <div class="text-base c-black font-semibold">{{ userInfo?.FullName }}</div>
              </div>

              <div class="fc gap-1 border-1px border-solid border-#cccccc p-2 rounded">
                <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Loại tài khoản:</div>
                <div class="text-base c-black font-semibold">
                  {{ isCustomer ? 'Tài khoản khách hàng' : 'Tài khoản nhân viên' }}
                </div>
              </div>
              <div class="fc gap-1 border-1px border-solid border-#cccccc p-2 rounded">
                <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Email:</div>
                <div class="text-base c-black font-semibold">{{ userInfo?.Email }}</div>
              </div>
              <div class="fc gap-1 border-1px border-solid border-#cccccc p-2 rounded">
                <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Số điện thoại:</div>
                <div class="text-base c-black font-semibold">{{ userInfo?.Phone }}</div>
              </div>
              <div class="fc gap-1 border-1px border-solid border-#cccccc p-2 rounded">
                <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Địa chỉ:</div>
                <div class="text-base c-black font-semibold">{{ userInfo?.address }}</div>
              </div>
              <div class="fc gap-1 border-1px border-solid border-#cccccc p-2 rounded">
                <div class="text-base font-normal c-black opacity-70 whitespace-nowrap">Khu vực kinh doanh:</div>
                <div class="text-base c-black font-semibold">{{ userInfo?.address }}</div>
              </div>
            </div>
          </div>

          <!-- Security Tab -->
          <div
            v-if="activeTab === 'security'"
            class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-full">
            <div class="p-6 border-b border-gray-200">
              <h2 class="text-xl font-semibold text-gray-900">Bảo mật tài khoản</h2>
            </div>

            <div class="p-6">
              <form @submit.prevent="changePassword" class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <BaseInputPassword
                  name="oldPassword"
                  label="Mật khẩu cũ"
                  placeholder="Mật khâu cũ"
                  :rules="{ required: true }"
                  v-model="form.oldPassword" />
                <BaseInputPassword
                  name="newPassword"
                  label="Mật khóa mới"
                  placeholder="Mật khâu mới"
                  :rules="{ required: true }"
                  v-model="form.newPassword" />
                <BaseInputPassword
                  name="confirmPassword"
                  label="Nhập lại mật khẩu mới"
                  placeholder="Nhập lại mật khẩu mới"
                  :rules="{ required: true, confirmed: 'newPassword' }"
                  v-model="form.confirmPassword" />
                <div class="col-span-1 lg:col-span-3 fr jc-fe">
                  <Button label="Cập nhật" severity="primary" type="submit" :loading="isLoading" />
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
