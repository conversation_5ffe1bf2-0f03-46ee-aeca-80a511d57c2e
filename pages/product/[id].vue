<script setup lang="ts">
import { logoDefault } from '~/assets/images'
import CardProductRelated from '~/components/common/CardProductRelated.vue'
import { useAppStore } from '~/stores/app'
const appStore = useAppStore()
const route = useRoute()
const selectedImage = ref('')
const selectedColor = ref({})
const selectedSize = ref({})
const quantity = ref(1)
const isInWishlist = ref(false)
const showSuccessMessage = ref(false)

// Sample product data
const product = ref<any>({})
const listProduct = ref<any>([])
const listImageStore = computed(() => appStore.listImage)
const listImage = ref<any>([])

const toast = useToast()
// Computed properties
const selectedVariant = computed(() => {
  return {}
})

const increaseQuantity = () => {
  product.value.quantity++
}
const decreaseQuantity = () => {
  product.value.quantity = Math.max(product.value.quantity - 1, 1)
}
const addToCart = () => {
  appStore.addAllCart(product.value)
  toast.add({ severity: 'success', summary: 'Thông báo', detail: 'Đã thêm sản phẩm vào giỏ hàng', life: 3000 })
}
const getDataInfo = async () => {
  appStore.isLoading = true

  const url = encodeURIComponent(`material:promoteList`)

  const { data }: any = await useApi(url, {
    method: 'GET',
    params: {
      sap_api_url: `ZALP_SALES_ORDER_SRV/PromoMatSet(Salesmatnumber='${route.query.Salesmatnumber}',Materialnumber='${route.params.id}')?$format=json`,
    },
  })
  //@ts-ignore
  product.value = { ...data.value?.d, quantity: 1 } || {}
  getDataList()
  appStore.isLoading = false
}
const getDataList = async () => {
  appStore.isLoading = true
  const url = encodeURIComponent(`sap_api_get`)

  const { data }: any = await useApi(url, {
    method: 'GET',
    params: {
      sap_api_url: `ZALP_SALES_ORDER_SRV/PromoMatSet?$filter=IsText eq 'X' and Salesmatnumber eq '${route.query.Salesmatnumber}'&$format=json`,
    },
  })
  listProduct.value =
    data.value?.d?.results?.map((el: any) => {
      el.quantity = 1
      return el
    }) || []

  if (data.value?.d?.results && data.value?.d?.results.length > 0) {
    product.value = data.value?.d?.results[0]
  }
  appStore.isLoading = false
}
const productRelated = computed(() => {
  return appStore.listMaterial
    .filter((el: any) => el.Salesmatnumber !== product.value.Salesmatnumber && el.Itemtype == product.value.Itemtype)
    .slice(0, 6)
})
const mapImage = () => {
  listImage.value =
    listImageStore.value
      .filter(
        (item: any) =>
          item.salesMatNumber == product.value.Salesmatnumber && item.materialNumber == product.value.Materialnumber,
      )
      ?.map((item: any) => item.images)[0] || []

  if (listImage.value.length > 0) {
    selectedImage.value = listImage.value[0]?.url
  }
}
const linkBrand = computed(() => {
  return '/convert-point?brand=' + encodeURIComponent(`${product.value.Brand}`)
})

getDataInfo()
watch(
  () => product.value,
  () => {
    mapImage()
  },
  {
    immediate: true,
  },
)
// Lifecycle
onMounted(() => {})
</script>
<template>
  <div class="container mx-a">
    <!-- Breadcrumb -->
    <nav class="flex mb-4" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-2">
        <li>
          <nuxt-link href="/convert-point" class="text-gray-500 hover:text-gray-700"> Sản phẩm </nuxt-link>
        </li>
        <li>
          <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path
              fill-rule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clip-rule="evenodd"></path>
          </svg>
        </li>
        <li>
          <nuxt-link :to="linkBrand" class="text-gray-500 hover:text-gray-700">{{ product?.Brand }}</nuxt-link>
        </li>
        <li>
          <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path
              fill-rule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clip-rule="evenodd"></path>
          </svg>
        </li>
        <li>
          <span class="c-primary font-medium">{{ product?.MaterialDescription }}</span>
        </li>
      </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-7 gap-12">
      <!-- Product Images -->
      <div class="space-y-4 col-span-3">
        <!-- Main Image -->
        <div class="aspect-square bg-white rounded-2xl overflow-hidden shadow-sm border border-gray-200">
          <img
            :src="selectedImage ? selectedImage : logoDefault"
            :alt="product?.MaterialDescription"
            class="w-full h-full object-cover" />
        </div>

        <!-- Thumbnail Images -->
        <div class="grid grid-cols-4 gap-4">
          <button
            v-for="(image, index) in listImage"
            :key="index"
            @click="selectedImage = image.url"
            class="aspect-square bg-white rounded-lg overflow-hidden border-2 transition-colors"
            :class="selectedImage === image ? 'border-blue-500' : 'border-gray-200 hover:border-gray-300'">
            <img
              :src="image?.url"
              :alt="`${product?.MaterialDescription} ${index + 1}`"
              class="w-full h-full object-cover" />
          </button>
        </div>
      </div>

      <!-- Product Info -->
      <div class="space-y-6 col-span-4">
        <!-- Title and Price -->
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ product?.MaterialDescription }}</h1>
          <div class="flex items-center space-x-4 mb-4">
            <span class="text-3xl font-bold text-blue-600"> {{ useFormatNumber(product?.Points) }} điểm</span>
            <!-- <span v-if="selectedVariant.originalPrice" class="text-xl text-gray-500 line-through">
                ${{ selectedVariant.originalPrice }}
              </span>
              <span
                v-if="selectedVariant.originalPrice"
                class="bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded">
                -{{ Math.round((1 - selectedVariant.price / selectedVariant.originalPrice) * 100) }}%
              </span> -->
          </div>

          <!-- Rating -->
          <div class="flex items-center space-x-2 mb-4">
            <span class="text-gray-600">0 lượt mua</span>
          </div>
        </div>

        <!-- Description
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Mô tả sản phẩm</h3>
            <p class="text-gray-600 leading-relaxed">{{ product.description }}</p>
          </div> -->

        <!-- Color Selection -->
        <div v-if="product?.Variationtype == 'C'">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">
            Màu sắc: <span class="font-normal text-gray-600">{{ product?.Variationvalue }}</span>
          </h3>
          <div class="flex space-x-3">
            <button
              v-for="color in listProduct"
              :key="color.Materialnumber"
              @click="product = color"
              class="w-10 h-10 rounded-full border-2 transition-all duration-200 relative"
              :class="
                color?.Materialnumber === product?.Materialnumber
                  ? 'border-gray-900 scale-110'
                  : 'border-gray-300 hover:border-gray-400'
              "
              :style="{ backgroundColor: `#${color?.Colorcode}` }"
              v-tooltip.top="color?.Colorname"
              :title="color.name">
              <span
                v-if="color?.Materialnumber === product?.Materialnumber"
                class="absolute inset-0 flex items-center justify-center">
                <svg class="w-4 h-4 text-white drop-shadow" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"></path>
                </svg>
              </span>
            </button>
          </div>
        </div>

        <!-- Size Selection -->
        <div v-if="product?.Variationtype == 'S'">
          <h3 class="text-lg font-semibold text-gray-900 mb-3">
            Kích thước: <span class="font-normal text-gray-600">{{ product?.Colorname }}</span>
          </h3>
          <div class="grid grid-cols-4 gap-3">
            <button
              v-for="size in listProduct"
              :key="size.Materialnumber"
              @click="product = size"
              class="py-3 px-4 border rounded-lg text-center font-medium transition-colors"
              :class="[
                product?.Materialnumber === size?.Materialnumber
                  ? 'border-primary bg-primary c-white'
                  : useParseNumber(size?.Quantity) > 0
                  ? 'border-gray-300 hover:border-gray-400 c-black bg-primary-20'
                  : 'border-gray-200 c-black  bg-black-20',
              ]">
              {{ size?.Variationvalue }}
            </button>
          </div>
        </div>

        <!-- Quantity -->
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-3">Số lượng</h3>
          <div class="flex items-center space-x-4">
            <div class="flex items-center border border-gray-300 rounded-lg">
              <button
                @click="decreaseQuantity"
                :disabled="product?.quantity <= 1"
                class="p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                </svg>
              </button>
              <span class="px-4 py-2 font-medium">{{ product?.quantity }}</span>
              <button
                @click="increaseQuantity"
                class="p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
              </button>
            </div>
            <span class="text-sm text-gray-600"> {{ useFormatNumber(product?.Quantity) }} sản phẩm có sẵn </span>
          </div>
        </div>

        <!-- Actions -->
        <div class="space-y-4">
          <button
            @click="addToCart"
            :disabled="useParseNumber(product?.Quantity) == 0"
            class="w-full fr jc-c bg-blue-600 text-white py-4 px-6 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
            <span v-if="useParseNumber(product?.Quantity) > 0">Thêm vào giỏ hàng</span>
            <span v-else>Hết hàng</span>
          </button>
        </div>

        <!-- Product Features -->
        <div class="border-t border-gray-200 pt-6 pb-10">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Thông tin sản phẩm</h3>
          <div class="space-y-3" v-html="product?.LongText"></div>
        </div>
      </div>
    </div>

    <div class="mt-10" v-if="productRelated.length > 0">
      <div class="fr jc-sb">
        <div class="com-heading">Sản phẩm tương tự</div>
        <nuxt-link
          to="/convert-point"
          class="c-primary text-base font-semibold cursor-pointer py-2 transition-all relative group">
          Xem thêm <img src="~/assets/icons/i-arrow-right.svg" alt="" />
          <div class="absolute w-0 h-[1.5px] bg-primary group-hover:w-full transition-all duration-300"></div>
        </nuxt-link>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mt-4">
        <CardProductRelated v-for="product in productRelated" :key="product.id" :product="product" />
      </div>
    </div>
  </div>
</template>
