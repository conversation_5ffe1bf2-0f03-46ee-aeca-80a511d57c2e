<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-3xl font-bold text-gray-900 mb-4"><PERSON><PERSON><PERSON> sách tích điểm</h1>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Mỗi đơn hàng trên 2.000.000đ sẽ tương ứng với số điểm của từng thương hiệu
        </p>
      </div>

      <!-- Brand Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
        <!-- ALP Brand -->
        <div
          class="group relative bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-xl overflow-hidden transform hover:scale-105 transition-all duration-300 hover:shadow-2xl">
          <!-- Background Pattern -->
          <div class="absolute inset-0 bg-white opacity-10">
            <div
              class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full opacity-20 transform translate-x-8 -translate-y-8"></div>
            <div
              class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full opacity-15 transform -translate-x-4 translate-y-4"></div>
          </div>

          <div class="relative p-8 text-center text-white">
            <!-- Logo -->
            <div
              class="w-24 h-24 bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-opacity-30 transition-all duration-300">
              <div class="text-3xl font-bold text-white">ALP</div>
            </div>

            <!-- Brand Name -->
            <h3 class="text-2xl font-bold mb-4">ALP</h3>

            <!-- Points -->
            <div class="mb-6">
              <div class="text-5xl font-black mb-2 group-hover:scale-110 transition-transform duration-300">1</div>
              <p class="text-blue-100 font-medium">điểm tích lũy</p>
            </div>

            <!-- Decorative Element -->
            <div class="absolute top-6 left-6 w-3 h-3 bg-white rounded-full opacity-60"></div>
            <div class="absolute top-10 left-8 w-2 h-2 bg-white rounded-full opacity-40"></div>
          </div>
        </div>

        <!-- FUJI Brand -->
        <div
          class="group relative bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl shadow-xl overflow-hidden transform hover:scale-105 transition-all duration-300 hover:shadow-2xl">
          <!-- Background Pattern -->
          <div class="absolute inset-0 bg-white opacity-10">
            <div
              class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full opacity-20 transform translate-x-8 -translate-y-8"></div>
            <div
              class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full opacity-15 transform -translate-x-4 translate-y-4"></div>
          </div>

          <div class="relative p-8 text-center text-white">
            <!-- Logo -->
            <div
              class="w-24 h-24 bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-opacity-30 transition-all duration-300">
              <div class="text-2xl font-bold text-white">FUJI</div>
            </div>

            <!-- Brand Name -->
            <h3 class="text-2xl font-bold mb-4">FUJI</h3>

            <!-- Points -->
            <div class="mb-6">
              <div class="text-5xl font-black mb-2 group-hover:scale-110 transition-transform duration-300">2</div>
              <p class="text-emerald-100 font-medium">điểm tích lũy</p>
            </div>

            <!-- Decorative Element -->
            <div class="absolute top-6 left-6 w-3 h-3 bg-white rounded-full opacity-60"></div>
            <div class="absolute top-10 left-8 w-2 h-2 bg-white rounded-full opacity-40"></div>
          </div>
        </div>

        <!-- DT Brand -->
        <div
          class="group relative bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl shadow-xl overflow-hidden transform hover:scale-105 transition-all duration-300 hover:shadow-2xl">
          <!-- Background Pattern -->
          <div class="absolute inset-0 bg-white opacity-10">
            <div
              class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full opacity-20 transform translate-x-8 -translate-y-8"></div>
            <div
              class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full opacity-15 transform -translate-x-4 translate-y-4"></div>
          </div>

          <div class="relative p-8 text-center text-white">
            <!-- Logo -->
            <div
              class="w-24 h-24 bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-opacity-30 transition-all duration-300">
              <div class="text-3xl font-bold text-white">DT</div>
            </div>

            <!-- Brand Name -->
            <h3 class="text-2xl font-bold mb-4">DT</h3>

            <!-- Points -->
            <div class="mb-6">
              <div class="text-5xl font-black mb-2 group-hover:scale-110 transition-transform duration-300">3</div>
              <p class="text-purple-100 font-medium">điểm tích lũy</p>
            </div>

            <!-- Decorative Element -->
            <div class="absolute top-6 left-6 w-3 h-3 bg-white rounded-full opacity-60"></div>
            <div class="absolute top-10 left-8 w-2 h-2 bg-white rounded-full opacity-40"></div>
          </div>
        </div>

        <!-- YNG Brand -->
        <div
          class="group relative bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl shadow-xl overflow-hidden transform hover:scale-105 transition-all duration-300 hover:shadow-2xl">
          <!-- Background Pattern -->
          <div class="absolute inset-0 bg-white opacity-10">
            <div
              class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full opacity-20 transform translate-x-8 -translate-y-8"></div>
            <div
              class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full opacity-15 transform -translate-x-4 translate-y-4"></div>
          </div>

          <div class="relative p-8 text-center text-white">
            <!-- Logo -->
            <div
              class="w-24 h-24 bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-opacity-30 transition-all duration-300">
              <div class="text-3xl font-bold text-white">YNG</div>
            </div>

            <!-- Brand Name -->
            <h3 class="text-2xl font-bold mb-4">YNG</h3>

            <!-- Points -->
            <div class="mb-6">
              <div class="text-5xl font-black mb-2 group-hover:scale-110 transition-transform duration-300">4</div>
              <p class="text-orange-100 font-medium">điểm tích lũy</p>
            </div>

            <!-- Decorative Element -->
            <div class="absolute top-6 left-6 w-3 h-3 bg-white rounded-full opacity-60"></div>
            <div class="absolute top-10 left-8 w-2 h-2 bg-white rounded-full opacity-40"></div>
          </div>
        </div>
      </div>

      <!-- Additional Information -->
      <div class="mt-12 bg-white rounded-lg shadow-sm p-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">Thông tin chi tiết</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- How to earn points -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Cách tích điểm
            </h3>
            <ul class="space-y-3 text-gray-600">
              <li class="flex items-start">
                <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span>Mua sắm với đơn hàng từ 2.000.000đ trở lên</span>
              </li>
              <li class="flex items-start">
                <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span>Điểm được tích lũy theo từng thương hiệu riêng biệt</span>
              </li>
              <li class="flex items-start">
                <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span>Điểm được cộng tự động sau khi đơn hàng hoàn thành</span>
              </li>
            </ul>
          </div>

          <!-- How to use points -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <svg class="w-5 h-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
              Cách sử dụng điểm
            </h3>
            <ul class="space-y-3 text-gray-600">
              <li class="flex items-start">
                <span class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span>Quy đổi điểm thành voucher giảm giá</span>
              </li>
              <li class="flex items-start">
                <span class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span>Sử dụng điểm để thanh toán một phần đơn hàng</span>
              </li>
              <li class="flex items-start">
                <span class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                <span>Đổi quà tặng độc quyền từ các thương hiệu</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- Contact Support -->
        <div class="mt-8 pt-8 border-t border-gray-200 text-center">
          <p class="text-gray-600 mb-4">Có thắc mắc về chính sách tích điểm?</p>
          <div class="flex justify-center space-x-4">
            <button class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Liên hệ hỗ trợ
            </button>
            <button
              class="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors">
              Xem điểm của tôi
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// No reactive data needed for this static page
</script>
