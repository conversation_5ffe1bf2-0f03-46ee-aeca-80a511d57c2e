<script lang="ts" setup>
import { useAppStore } from '~/stores/app'

const appStore = useAppStore()
const form = ref({
  material: [] as any,
  plant: '',
  Lgort: '',
})
const isLoading = ref(false)
const listData = ref<any>([])
const getData = async () => {
  isLoading.value = true
  let materialFilter
  if (form.value.material?.length > 0) {
    materialFilter = form.value.material.map((mat: any) => `Matnr eq '${mat}'`).join(' or ')
  } else {
    materialFilter = ``
  }

  const queryFilter = `Werks eq '${form.value.plant}' and Lgort eq '${form.value.Lgort}' and (${materialFilter})`
  const { data, loading }: any = await useApi('sap_api_get', {
    method: 'GET',
    params: {
      sap_api_url: `ZREAD_MATERIALS_SRV/MaterialsSet?$filter=${queryFilter}`,
    },
  })
  isLoading.value = loading
  listData.value = data.value?.d?.results || []
}
</script>

<template>
  <div class="container mx-a fc gap-4">
    <div class="page-heading">Kiểm tra tồn kho</div>
    <div class="bg-white p-4 rounded grid grid-cols-5 gap-4">
      <SearchMaterial v-model:material="form.material" class="col-span-2" />
      <BaseInputText label="Mã nhà máy" :rules="{ required: false }" v-model="form.plant" />
      <BaseInputText label="Mã kho" :rules="{ required: false }" v-model="form.Lgort" />
      <div class="fc gap-2">
        <div class="h-24px"></div>
        <Button label="Tìm kiếm" severity="primary" :loading="isLoading" @click="getData" />
        <div class="flex-1"></div>
      </div>
    </div>
    <div class="bg-white fc gap-4 p-4 rounded w-full">
      <div class="com-heading">Danh sách sản phẩm</div>

      <div class="grid grid-cols-2">
        <!-- <div class="bg-red h-100">qqq</div> -->
        <DataTable
          :value="listData"
          dataKey="_id"
          class="w-full col-span-2"
          rowHover
          scrollable
          scroll-height="calc(100vh - 350px)"
          :rows="1000"
          :loading="isLoading">
          <Column header="#" :frozen="true" alignFrozen="left">
            <template #body="slotProps">
              {{ slotProps.index + 1 }}
            </template>
          </Column>

          <Column field="Matnr" header="Mã sản phẩm" style="min-width: 200px" :frozen="true" alignFrozen="left">
          </Column>
          <Column field="Maktx" header="Tên sản phẩm" style="min-width: 200px" :frozen="true" alignFrozen="left">
          </Column>
          <Column field="Lgobe" header="Loại sản phẩm" style="min-width: 150px" :frozen="true" alignFrozen="left">
          </Column>
          <Column field="Matkl" header="Nhóm sản phẩm" style="min-width: 200px"> </Column>
          <Column field="Werks" header="Mã nhà máy" style="min-width: 150px"> </Column>
          <Column field="Lgort" header="Mã kho" style="min-width: 150px"> </Column>
          <Column field="Lgobe" header="Tên kho" style="min-width: 150px"> </Column>
          <Column field="Labst" header="Số lượng tồn" style="min-width: 150px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Labst) }}
            </template></Column
          >
          <Column field="Meins" header="Đ/v bán hàng" style="min-width: 150px"> </Column>
          <Column field="Cwm_labst" header="Số lượng tồn" style="min-width: 150px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Cwm_labst) }}
            </template>
          </Column>
          <Column field="Cwm_meins" header="Đ/v khác" style="min-width: 150px"> </Column>
          <Column field="Labst_bo" header="Số lượng tồn (Bó)" style="min-width: 200px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Labst_bo) }}
            </template>
          </Column>

          <template #empty>
            <EmptyState />
          </template>
        </DataTable>
      </div>
    </div>
  </div>
</template>
