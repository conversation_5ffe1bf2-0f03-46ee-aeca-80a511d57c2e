<script lang="ts" setup>
import { logoALP, logoAvt, logoDT, logoFuji, logoREVO, logoYNGHUA } from '~/assets/images'
import BaseAvatar from '~/components/common/BaseAvatar.vue'
import { useAppStore } from '~/stores/app'
import PDF from 'pdf-vue3'

const posts = ref<any>([])
const appStore = useAppStore()
const brand = ref('')
const mapBrand = (brand: any) => {
  switch (brand) {
    case 'Đại Tân':
      return {
        name: 'Đại Tân',
        logo: logoDT,
      }
    case 'Fuji':
      return {
        name: '<PERSON>',
        logo: logoFuji,
      }
    case 'Revo':
      return {
        name: 'Revo',
        logo: logoREVO,
      }
    case 'YNGHUA':
      return {
        name: 'YNGHUA',
        logo: logoYNGHUA,
      }

    default:
      return {
        name: 'An Lậ<PERSON> Phát',
        logo: logoALP,
      }
  }
}
const pricings = computed(() => {
  if (!brand.value) return posts.value
  return posts.value.filter((item: any) => item.brandName === brand.value)
})
const getData = async () => {
  //appStore.isLoading = true
  const url = encodeURIComponent(`pricing:list`)
  const { data }: any = await useApi(url, {
    method: 'GET',
  })
  console.log(data.value, 'value')
  posts.value = data.value?.data.filter((item: any) => item.isEnabled === true) || []
  appStore.isLoading = false
}

getData()
</script>

<template>
  <div class="container mx-auto min-h-screen">
    <div class="page-heading mb-4">Bảng giá</div>
    <div class="fr ai-c gap-6 p-2 rounded-xl bg-white">
      <div
        class="fc ai-c gap-2 p-2 rounded-lg cursor-pointer transition-all duration-300 hover:bg-gray-50 hover:-translate-y-1 hover:shadow-md border-2 border-transparent hover:border-blue-100 min-w-[100px]"
        :class="brand === '' ? 'bg-primary-20 shadow-md border-blue-200' : 'bg-gray-50'"
        @click="brand = ''">
        <img src="@/assets/images/all-product.svg" alt="Tất cả" class="w-12 h-12 object-contain rounded-lg" />
        <span class="text-sm font-medium text-center" :class="brand === '' ? 'c-primary font-semibold' : 'c-black-80'"
          >Tất cả</span
        >
      </div>
      <div
        class="fc ai-c gap-2 p-2 rounded-lg cursor-pointer transition-all duration-300 hover:bg-gray-50 hover:-translate-y-1 hover:shadow-md border-2 border-transparent hover:border-blue-100 min-w-[100px]"
        :class="brand === 'Revo' ? 'bg-primary-20 shadow-md border-blue-200' : 'bg-gray-50'"
        @click="brand = 'Revo'">
        <img src="@/assets/images/logo-4-col.png" alt="Revo" class="w-12 h-12 object-contain rounded-lg" />
        <span
          class="text-sm font-medium text-center"
          :class="brand === 'Revo' ? 'c-primary font-semibold' : 'c-black-80'"
          >Revo</span
        >
      </div>
      <div
        class="fc ai-c gap-2 p-2 rounded-lg cursor-pointer transition-all duration-300 hover:bg-gray-50 hover:-translate-y-1 hover:shadow-md border-2 border-transparent hover:border-blue-100 min-w-[100px]"
        :class="brand === 'Đại Tân' ? 'bg-primary-20 shadow-md border-blue-200' : 'bg-gray-50'"
        @click="brand = 'Đại Tân'">
        <img src="@/assets/images/logo-3.png" alt="Đại Tân" class="w-12 h-12 object-contain rounded-lg" />
        <span
          class="text-sm font-medium text-center"
          :class="brand === 'Đại Tân' ? 'c-primary font-semibold' : 'c-black-80'"
          >Đại Tân</span
        >
      </div>

      <div
        class="fc ai-c gap-2 p-2 rounded-lg cursor-pointer transition-all duration-300 hover:bg-gray-50 hover:-translate-y-1 hover:shadow-md border-2 border-transparent hover:border-blue-100 min-w-[100px]"
        :class="brand === 'Fuji' ? 'bg-primary-20 shadow-md border-blue-200' : 'bg-gray-50'"
        @click="brand = 'Fuji'">
        <img src="@/assets/images/logo-2.png" alt="Revo" class="w-12 h-12 object-contain rounded-lg" />
        <span
          class="text-sm font-medium text-center"
          :class="brand === 'Fuji' ? 'c-primary font-semibold' : 'c-black-80'"
          >Fuji</span
        >
      </div>
      <div
        class="fc ai-c gap-2 p-2 rounded-lg cursor-pointer transition-all duration-300 hover:bg-gray-50 hover:-translate-y-1 hover:shadow-md border-2 border-transparent hover:border-blue-100 min-w-[100px]"
        :class="brand === 'YNGHUA' ? 'bg-primary-20 shadow-md border-blue-200' : 'bg-gray-50'"
        @click="brand = 'YNGHUA'">
        <img src="@/assets/images/logo-1.png" alt="Revo" class="w-12 h-12 object-contain rounded-lg" />
        <span
          class="text-sm font-medium text-center"
          :class="brand === 'YNGHUA' ? 'c-primary font-semibold' : 'c-black-80'"
          >YNGHUA</span
        >
      </div>
    </div>
    <!-- Blog Cards Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mt-6">
      <nuxt-link
        v-for="post in pricings"
        :key="post.id"
        :to="`/pricing/${post.id}`"
        class="pricing-card bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer group">
        <!-- Image -->
        <div class="relative h-48 overflow-hidden">
          <!-- <img
              :src="post.url"
              :alt="post.title"
              class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" /> -->
          <!-- <iframe
              :src="post.fileUrl"
              class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              frameborder="0"></iframe> -->
          <PDF
            class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 overflow-y-hidden"
            :src="post?.fileUrl"
            :showPageTooltip="false"
            :showProgress="true" />
        </div>

        <!-- Content -->
        <div class="p-5">
          <h3 class="text-lg font-bold c-black mb-2 line-clamp-2 group-hover:c-primary transition-colors">
            {{ post.title }}
          </h3>
          <!-- <p class="text-gray-600 text-sm mb-4 line-clamp-3">
              {{ post.excerpt }}
            </p> -->

          <!-- Author & Date -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <!-- <BaseAvatar :type="'square'" :size="32" :url="mapBrand(post.brandName).logo" /> -->
              <img :src="mapBrand(post.brandName).logo" class="h-36px w-auto object-cover" alt="" />
              <div>
                <p class="text-base font-semibold c-text">{{ mapBrand(post.brandName).name }}</p>
                <p class="text-xs text-gray-500">{{ useMoment(post.createdDate) }}</p>
              </div>
            </div>
          </div>
        </div>
      </nuxt-link>
    </div>
    <EmptyState v-if="posts.length === 0" />
  </div>
</template>
