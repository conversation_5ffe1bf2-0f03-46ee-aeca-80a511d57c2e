<script lang="ts" setup>
import FormNews from '~/components/news/FormNews.vue'
const form = ref<any>({
  objectType: 'CUSTOMER',
})
const toast = useToast()
const router = useRouter()
const handleSubmit = async (form: any) => {
  const url = encodeURIComponent('news:create')
  const body = {
    title: form.title,
    content: form.content,
    type: 'NEWS',
    objectType: form.objectType,
    url: form.thumbnailUrl,
  }
  const { data }: any = await useApi(url, {
    method: 'POST',
    body,
  })
  if (data.value?.data) {
    toast.add({ severity: 'success', summary: 'Thông báo', detail: 'Thêm mới thành công', life: 3000 })
    router.push('/admin/news')
  }
}
</script>

<template>
  <div class="container mx-a fc gap-4">
    <div class="bg-white fc gap-4 p-4 rounded w-full">
      <div class="com-heading">T<PERSON>o bà<PERSON> viết</div>
      <FormNews v-model:form="form" @submit="handleSubmit" />
    </div>
  </div>
</template>
