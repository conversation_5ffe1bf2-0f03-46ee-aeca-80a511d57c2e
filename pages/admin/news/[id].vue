<script lang="ts" setup>
import FormNews from '~/components/news/FormNews.vue'
const form = ref<any>({
  objectType: 'CUSTOMER',
})
const toast = useToast()
const router = useRouter()
const route = useRoute()
const getData = async () => {
  const route = useRoute()
  const url = encodeURIComponent(`news:detail`) + `?id=${route.params.id}`
  const { data }: any = await useApi(url, {
    method: 'GET',
    params: {
      id: route.params.id,
    },
  })
  console.log(data.value, 'value')
  form.value = data.value?.data || {}
  form.value.thumbnailUrl = form.value.url
}
getData()
const handleSubmit = async (form: any) => {
  const url = encodeURIComponent('news:update')
  const body = {
    title: form.title,
    content: form.content,
    type: 'NEWS',
    objectType: form.objectType,
    url: form.thumbnailUrl,
    id: route.params.id,
  }
  const { data }: any = await useApi(url, {
    method: 'PUT',
    body,
  })
  if (data.value?.data) {
    toast.add({ severity: 'success', summary: 'Thông báo', detail: 'Thêm mới thành công', life: 3000 })
    router.push('/admin/news')
  }
}
</script>

<template>
  <div class="container mx-a fc gap-4">
    <div class="bg-white fc gap-4 p-4 rounded w-full">
      <div class="com-heading">Tạo bài viết</div>
      <FormNews v-model:form="form" @submit="handleSubmit" @edit="handleSubmit" />
    </div>
  </div>
</template>
