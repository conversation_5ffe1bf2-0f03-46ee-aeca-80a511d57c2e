<script lang="ts" setup>
import FormPricing from '~/components/pricing/FormPricing.vue'
import { useAppStore } from '~/stores/app'

const appStore = useAppStore()

const { $auth: auth, $dayjs: dayjs }: any = useNuxtApp()
const isLoading = ref(false)
const listData = ref<any>([])
const totalRecord = ref(0)
const perPage = ref(20)
const confirm = useConfirm()
const toast = useToast()
const filterPanel = ref()
const config = useRuntimeConfig()
const isShowForm = ref(false)
const formRecord = ref<any>({})
const query = ref({
  search: '',
})

const dataFilter = computed(() => {
  return listData.value.filter(filterData)
})

const getData = async () => {
  try {
    isLoading.value = true
    const url = encodeURIComponent(`pricing:list`)
    const { data }: any = await useApi(url, {
      method: 'GET',
    })
    listData.value = data.value?.data || []
    totalRecord.value = data.value?.data.length || 0
    isLoading.value = false
  } catch (error) {
  } finally {
    isLoading.value = false
  }
}
getData()

const filterData = (el: any) => {
  if (query.value.search) {
    const keyword = query.value.search.toLowerCase()
    const materialNumberMatch = el.brandName.toLowerCase().includes(keyword)
    const salesMatNumberMatch = el.title.toLowerCase().includes(keyword)
    if (!materialNumberMatch && !salesMatNumberMatch) {
      return false
    }
  }

  return true
}
const editRecord = (record: any) => {
  console.log(record, 'record')
  isShowForm.value = true
  formRecord.value = { ...record }
}
const removeRecord = (record: any) => {
  confirm.require({
    message: 'Bạn có chắc chắn muốn xóa không?',
    header: 'Xác nhận',
    icon: 'pi pi-exclamation-triangle',
    rejectLabel: 'Hủy',
    acceptLabel: 'Xóa',
    acceptClass: 'p-button-danger',
    rejectClass: 'p-button-help',
    accept: async () => {
      const url = encodeURIComponent(`pricing:delete`) + `?id=${record.id}`
      const { data }: any = await useApi(url, {
        method: 'PUT',
      })
      if (data.value?.data) {
        getData()
        toast.add({ severity: 'success', summary: 'Thông báo', detail: 'Xóa thành công', life: 3000 })
      }
    },
  })
}

const changeStatus = async (record: any) => {
  try {
    isLoading.value = true
    const url = !record.isEnabled
      ? encodeURIComponent(`pricing:disable`) + `?id=${record.id}`
      : encodeURIComponent(`pricing:enable`) + `?id=${record.id}`
    const { data }: any = await useApi(url, {
      method: 'PUT',
    })
    if (data.value?.data) {
      getData()
      toast.add({ severity: 'success', summary: 'Thông báo', detail: 'Cập nhật thành công', life: 3000 })
    }
  } catch (error) {
  } finally {
    isLoading.value = false
  }
}
const handleSubmit = async (form: any) => {
  try {
    isLoading.value = true

    await fetch(config.public.apiBase + `/pricing:create`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${auth.strategy?.token.get()}`,
      },
      body: form,
    })
      .then((response) => response.json())
      .then((data) => {
        console.log(data, 'data')
        if (data?.data?.id) {
          isShowForm.value = false
          formRecord.value = {}
          getData()
          toast.add({ severity: 'success', summary: 'Thông báo', detail: 'Thêm mới thành công', life: 3000 })
          isLoading.value = false
        } else {
          toast.add({ severity: 'error', summary: 'Thông báo', detail: 'Thêm mới thất bại', life: 3000 })
        }
      })
      .finally(() => {
        isLoading.value = false
      })
  } catch (error) {
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div class="container mx-a fc gap-4">
    <div class="bg-white fc gap-4 p-4 rounded w-full">
      <div class="com-heading">Bảng giá</div>
      <div class="fr ai-c jc-fe gap-4">
        <BaseInputText placeholder="Tìm kiếm" class="flex-1" v-model="query.search" />
        <!-- <Button class="w-160px fr jc-c" @click="filterPanel.toggle($event)">
          <div class="fr ai-c gap-2"><img src="~/assets/icons/i-filter-white.svg" alt="" /> Bộ lọc</div>
        </Button>
        <OverlayPanel class="min-w-[320px] [&>.p-overlaypanel-content]:p-0" ref="filterPanel">
          <div class="grid grid-cols-1 p-4 gap-4">
            <div class="fr ai-c gap-4">
              <Checkbox v-model="query.roles" inputId="ROLE_CUSTOMER" name="roles" value="ROLE_CUSTOMER" />
              <label class="text-base font-normal c-black-90" for="ingredient3">Khách hàng</label>
            </div>
            <div class="fr ai-c gap-4">
              <Checkbox v-model="query.roles" inputId="ROLE_CUSTOMER_ORDER" name="roles" value="ROLE_CUSTOMER_ORDER" />
              <label class="text-base font-normal c-black-90" for="ingredient3">Khách hàng đặt hàng</label>
            </div>
            <div class="fr ai-c gap-4">
              <Checkbox v-model="query.roles" inputId="ROLE_SALE_ADMIN" name="roles" value="ROLE_SALE_ADMIN" />
              <label class="text-base font-normal c-black-90" for="ingredient3">Sale Admin</label>
            </div>
            <div class="fr ai-c gap-4">
              <Checkbox v-model="query.roles" inputId="ROLE_SALE_PERSON" name="roles" value="ROLE_SALE_PERSON" />
              <label class="text-base font-normal c-black-90" for="ingredient3">Sale Person</label>
            </div>
            <BaseCheckbox name="isLogged" label="Đã đăng nhập" v-model="query.isLogged" />
            <BaseCheckbox name="isUpdated" label="Đã cập nhật" v-model="query.isUpdated" />
          </div>
        </OverlayPanel> -->
        <!-- <download-excel :data="dataExcel" :json_fields="json_fields" worksheet="My Worksheet" name="users.xls">
          <Button label="Xuất danh sách" severity="primary" class="w-160px fr jc-c" />
        </download-excel> -->
        <Button label="Thêm bảng giá" severity="primary" class="w-160px fr jc-c" @click="isShowForm = true" />
      </div>
      <div class="grid grid-cols-2">
        <DataTable
          :value="dataFilter"
          dataKey="_id"
          class="w-full col-span-2"
          rowHover
          :rows="perPage"
          paginator
          scrollable
          :rows-per-page-options="[20, 50, 100]"
          :total-records="totalRecord"
          scroll-height="calc(100vh - 270px)"
          :loading="isLoading">
          <Column header="#" :frozen="true" alignFrozen="left">
            <template #body="slotProps">
              {{ slotProps.index + 1 }}
            </template>
          </Column>

          <Column field="title" header="Tên" style="min-width: 200px" :frozen="true" alignFrozen="left"> </Column>
          <Column field="brandName" header="Thương hiệu" style="min-width: 200px"> </Column>
          <Column field="fileName" header="File" style="min-width: 200px">
            <template #body="slotProps">
              <a :href="slotProps.data.fileUrl" target="_blank">{{ slotProps.data.fileName }}</a>
            </template>
          </Column>
          <Column field="isEnabled" header="Trạng thái" style="min-width: 140px">
            <template #body="slotProps">
              <BaseSwitch
                v-model="slotProps.data.isEnabled"
                @click="changeStatus(slotProps.data)"
                :disabled="slotProps.data.id === auth?.user?.id" />
            </template>
          </Column>

          <Column field="action" header="Hành động" style="min-width: 120px" :frozen="true" alignFrozen="right">
            <template #body="slotProps">
              <div class="fr ai-c gap-4">
                <img
                  src="~/assets/icons/i-trash-red.svg"
                  alt=""
                  class="cursor-pointer"
                  @click="removeRecord(slotProps.data)" />
                <!-- <img
                  src="~/assets/icons/i-edit.svg"
                  class="cursor-pointer"
                  @click="editRecord(slotProps.data)"
                  alt="" /> -->
              </div>
            </template>
          </Column>

          <template #empty>
            <EmptyState />
          </template>
        </DataTable>
      </div>
    </div>
    <BaseDialog
      v-model:visible="isShowForm"
      :title="`${formRecord.id ? 'Chỉnh sửa' : 'Thêm'} bảng giá`"
      :style="{ width: '800px' }">
      <FormPricing
        v-model:form="formRecord"
        @submit="handleSubmit"
        :isLoading="isLoading"
        @cancel=";(isShowForm = false), (formRecord = {})" />
    </BaseDialog>
  </div>
</template>
