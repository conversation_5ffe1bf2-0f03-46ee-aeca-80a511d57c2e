<script setup lang="ts">
import { useForm } from 'vee-validate'
import { useAppStore } from '~/stores/app'
const router = useRouter()
const appStore = useAppStore()
const showSuccessModal = ref(false)
const { $auth: auth }: any = useNuxtApp()
const { isCustomer } = useRole()
const sapCustomerId = ref(auth.user?.sapCustomerId || '')
const username = ref(auth.user?.username || '')
const listCart = computed(() => appStore.listCart)
const listAddress = ref<any>([])
const toast = useToast()
const { $dayjs: dayjs } = useNuxtApp()
const result = ref<any>({})

const orderForm = ref<any>({
  FullName: '',
  sapCustomerId: isCustomer ? sapCustomerId.value : username.value,
  shipToParty: '',
  shipToName: '',
  note: '',
  address: '',
  phone: '',
})
const isLoading = ref(false)
const calculateTotal = computed(() => {
  return listCart.value.reduce((total: number, item: any) => total + useParseNumber(item?.Points) * item.quantity, 0)
})
const { handleSubmit, resetForm } = useForm()
const onSubmit = handleSubmit(async () => {
  try {
    isLoading.value = true
    const body = {
      OrderNumber: '',
      SAPOrderNumber: '',
      // Customer: this.form.customer || '',
      UserName: username.value,
      CustomerName: '',
      OrderDate: `/Date(${dayjs().valueOf()})/`,
      DeliveryDate: `/Date(${dayjs().valueOf()})/`,
      UnixORDate: `/Date(${dayjs().valueOf()})/`,
      UnixDLDate: `/Date(${dayjs().valueOf()})/`,
      ShipToParty: orderForm.value.sapCustomerId,
      ShipToName: orderForm.value.shipToName,
      ShipToAddress: '',
      // SalesOrg: this.form.SalesOrg,
      // DistributionChannel: this.form.DistributionChannel,
      // Division: this.form.Division,
      OrderType: 'ZFOC',
      Status: '20',
      StatusText: '',
      TotalPrice: '0.00',
      Currency: 'VND',
      TotalWeight: '0.00',
      WeightUnit: 'KG',
      Note: orderForm.value.note,
      RejectReason: '',
      FeUserId: auth.user.id.toString(),
      CreatedDate: `/Date(${dayjs().valueOf()})/`,
      CreatedBy: username.value,
      ChangedDate: `/Date(${dayjs().valueOf()})/`,
      ChangedBy: '',
      Address: orderForm.value.address,
      Phone: orderForm.value.phone,
      Items: listCart.value.map((el: any, index: number) => {
        const data = {
          // SalesMaterial: el?.SalesMaterial?.toString() || '',
          Material: el?.Materialnumber || '',
          // OrderUnit: el?.Dvt?.toString() || '',
          // Status: el?.Status?.toString() || '',
          // MaterialDescription: el?.MaterialDescription?.toString() || '',
          OrderQuantity: el?.quantity?.toString() || '',
          // Points: el?.Points?.toString() || '',
          // VariantType: el?.VariantType?.toString() || '',
          // VariantValue: el?.VariantValue?.toString() || '',
          // PortalDescription: el?.PortalDescription?.toString() || '',
          // ColorCode: el?.ColorCode?.toString() || '',
          // Filename: el?.Filename?.toString() || '',
          // Hasattachment: el?.Hasattachment?.toString() || '',
        }
        return data
      }),
    }
    const { data }: any = await useApi(`so_create?sap_api_url=ZALP_SALES_ORDER_SRV/SOHeader`, {
      method: 'POST',
      body,
    })
    console.log(data.value, 'submit')
    if (data.value?.error) {
      toast.add({ severity: 'error', summary: 'Thông báo', detail: 'Đã xảy ra sự cố khi đặt hàng', life: 3000 })
    }
    if (data.value?.d) {
      // toast.add({
      //   severity: 'success',
      //   summary: 'Thông báo',
      //   detail: `Đơn hàng ${data.value?.d?.OrderNumber} đã đặt thành công`,
      //   life: 3000,
      // })
      appStore.listCart = []
      result.value = data.value?.d
      showSuccessModal.value = true
    }
    isLoading.value = false
  } catch (error) {
  } finally {
    isLoading.value = false
  }
})
const getData = async () => {
  const { data }: any = await useApi(`sap_api_get`, {
    method: 'GET',
    params: {
      sap_api_url: isCustomer
        ? `ZALP_SALES_ORDER_SRV/ShipToParty?$filter=Customer eq '${sapCustomerId.value}'&$format=json`
        : `ZALP_SALES_ORDER_SRV/SaleAddressSet?$filter=Username eq '${username.value}'&$format=json`,
    },
  })
  console.log(data.value, isCustomer, 'data')
  listAddress.value = data.value?.d?.results || []

  if (listAddress.value.length > 0) {
    if (isCustomer == false) {
      listAddress.value = listAddress.value?.map((el: any) => {
        el.Customer = el?.Customer || el?.Username
        return el
      })
    }
    orderForm.value.shipToParty = listAddress.value[0].Address
    orderForm.value.shipToName = listAddress.value[0].FullName
    if (!isCustomer) {
      orderForm.value.FullName = listAddress.value[0].FullName
    }
  }
}
const continueShopping = () => {
  router.push('/convert-point')
}
const viewOrderDetails = () => {
  showSuccessModal.value = false
  router.push(`/history/point/${result.value?.OrderNumber}`)
}
getData()
watch(
  () => orderForm.value.sapCustomerId,
  (val) => {
    if (isCustomer) {
      const item: any = listAddress.value.find((item: any) => item.Customer === val)
      if (item) {
        orderForm.value.shipToParty = item.Address
        orderForm.value.shipToName = item.FullName
        // orderForm.value.Customer = item?.Customer || item?.Username || ''
      }
    }
  },
)
watch(
  () => orderForm.value.FullName,
  (val) => {
    if (!isCustomer) {
      const item: any = listAddress.value.find((item: any) => item.FullName === val)
      if (item) {
        orderForm.value.shipToParty = item.Address
        orderForm.value.shipToName = item.FullName
        // orderForm.value.sapCustomerId = item?.Customer || item?.Username || ''
      }
    }
  },
)
</script>

<template>
  <div class="container mx-auto min-h-80vh">
    <!-- Header -->
    <div class="mb-4">
      <h1 class="page-heading">Giỏ hàng của bạn</h1>
      <p class="text-gray-600 mt-2">{{ listCart.length }} sản phẩm trong giỏ hàng</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Cart Items -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6">
            <h2 class="com-heading mb-6">Sản phẩm đã chọn</h2>

            <!-- Empty Cart -->
            <div v-if="listCart.length === 0" class="text-center py-12">
              <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293A1 1 0 005 16h12M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
              </svg>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Giỏ hàng trống</h3>
              <p class="text-gray-600">Thêm sản phẩm vào giỏ hàng để bắt đầu mua sắm</p>
            </div>

            <!-- Cart Items List -->
            <div v-else class="space-y-6">
              <CartItem
                v-for="item in listCart"
                :key="`${item?.Salesmatnumber}-${item?.Materialnumber}`"
                :item="item" />
            </div>
          </div>
        </div>
      </div>

      <!-- Order Summary & Checkout -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 sticky top-8">
          <div class="p-6">
            <h2 class="com-heading mb-6">Thông tin đặt hàng</h2>

            <!-- Order Summary -->
            <div class="space-y-3 mb-6">
              <div class="flex justify-between text-gray-600">
                <span>Tạm tính ({{ listCart.length }} sản phẩm)</span>
                <span>{{ useFormatNumber(calculateTotal) }} điểm</span>
              </div>
              <div class="flex justify-between text-gray-600">
                <span>Phí vận chuyển</span>
                <span>Miễn phí</span>
              </div>
              <div class="flex justify-between text-green-600">
                <span>Giảm giá</span>
                <span>0</span>
              </div>
              <div class="border-t border-gray-200 pt-3">
                <div class="flex justify-between text-lg font-semibold text-gray-900">
                  <span>Tổng cộng</span>
                  <span>{{ useFormatNumber(calculateTotal) }} điểm</span>
                </div>
              </div>
            </div>

            <!-- Customer Information Form -->
            <form autocomplete="off" @submit.prevent="onSubmit" class="space-y-4">
              <h3 class="com-heading mb-4">Thông tin giao hàng</h3>

              <!-- Full Name -->

              <BaseInputSelect
                v-if="isCustomer"
                :options="listAddress"
                optionLabel="FullName"
                optionValue="Customer"
                v-model="orderForm.sapCustomerId"
                label="Khách hàng"
                name="sapCustomerId"
                placeholder="Nhập khách hàng" />
              <!-- <BaseInputSelect
                v-else
                :options="listAddress"
                optionLabel="FullName"
                optionValue="FullName"
                v-model="orderForm.FullName"
                label="Nơi giao"
                name="sapCustomerId"
                placeholder="Nhập khách hàng" /> -->
              <BaseInputText
                v-if="isCustomer"
                v-model="orderForm.sapCustomerId"
                :disabled="true"
                label="Mã khách hàng"
                name="sapCustomerId"
                placeholder="Nhập mã khách hàng" />
              <!-- <BaseInputText
                v-model="orderForm.shipToName"
                :disabled="true"
                label="Tên khách hàng"
                name="shipToName"
                placeholder="Nhập tên khách hàng" /> -->
              <BaseInputTextArea
                v-model="orderForm.shipToParty"
                :disabled="isCustomer"
                label="Địa chỉ"
                name="shipToParty"
                :rows="2"
                placeholder="Nhập địa chỉ" />
              <BaseInputText
                v-model="orderForm.phone"
                label="Số điện thoại"
                name="phone"
                placeholder="Nhập số điện thoại" />
              <BaseInputTextArea v-model="orderForm.note" label="Ghi chú" name="note" placeholder="Nhập ghi chú" />
              <!-- Place Order Button -->
              <button
                type="submit"
                :disabled="!orderForm.shipToParty || !orderForm.shipToName || isLoading || listCart.length === 0"
                class="w-full fr ai-c jc-c bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                <span v-if="isLoading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Đang xử lý...
                </span>
                <span v-else>Đặt hàng ngay</span>
              </button>

              <p class="text-xs text-gray-500 text-center mt-3">
                Bằng việc đặt hàng, bạn đồng ý với
                <a href="#" class="text-blue-600 hover:text-blue-700">Điều khoản dịch vụ</a>
                và
                <a href="#" class="text-blue-600 hover:text-blue-700">Chính sách bảo mật</a>
              </p>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Modal -->
    <div
      v-if="showSuccessModal"
      class="fixed inset-0 bg-[#00000020] bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
        <div class="text-center">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2 text-center">Đặt hàng thành công!</h3>
          <p class="text-gray-600 mb-4">
            Đơn hàng #{{ result?.OrderNumber }} đã được tạo thành công. Chúng tôi sẽ liên hệ với bạn trong thời gian sớm
            nhất.
          </p>
          <div class="space-y-3">
            <button
              @click="continueShopping"
              class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-center">
              Tiếp tục mua sắm
            </button>
            <button
              @click="viewOrderDetails"
              class="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors text-center">
              Xem chi tiết đơn hàng
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
