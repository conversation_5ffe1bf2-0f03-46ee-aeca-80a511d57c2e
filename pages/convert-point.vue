<script lang="ts" setup>
import { logoALP, logoFuji, logoREVO, logoYNGHUA } from '~/assets/images'
import { listBrand, listBrandCode, listBrandCodeMap } from '~/constants/brand'
import { useAppStore } from '~/stores/app'

const appStore = useAppStore()
const listBrands = computed(() => {
  let arr = [
    {
      value: '',
      label: 'Tất cả',
    },
  ]
  listBrandCodeMap.forEach((item) => {
    arr.push({
      value: item.code,
      label: item.name,
    })
  })
  return arr
})
const userPanel = ref()
const toggleUserPanel = (e: Event) => {
  userPanel.value.toggle(e)
}
const brand = ref(useRoute().query.brand || '') as any
const listProduct = computed(() =>
  appStore.listMaterial
    .reduce((acc: any, cur: any) => {
      if (acc.find((el: any) => el.Salesmatnumber === cur.Salesmatnumber)) return acc
      return [...acc, cur]
    }, [])
    .filter((el: any) => el.Brand.includes(brand.value))
    .sort((a: any, b: any) => {
      const itemA = a.Itemtype?.toUpperCase() || ''
      const itemB = b.Itemtype?.toUpperCase() || ''
      return itemA.localeCompare(itemB, 'vi')
    }),
)
</script>

<template>
  <div class="container mx-a fc gap-4 min-h-screen">
    <div class="grid grid-cols-7 gap-4">
      <div class="col-span-3 fc jc-c">
        <div class="page-heading fr jc-sb ai-s grid grid-cols-1 md:grid-cols-2">Đổi điểm tích lũy</div>
      </div>
      <div class="w-full col-span-3">
        <BaseInputText placeholder="Tìm kiếm" class="w-full" v-model="brand" />
      </div>
      <!-- <BaseAvatar class="cursor-pointer" :size="40" :url="$auth?.user?.avatar" @click="toggleUserPanel" /> -->
      <div class="fc jc-fe">
        <Button @click="toggleUserPanel" severity="primary" class="fr ai-c jc-c gap-2 h-[40px] mb-[2px]">
          <img src="@/assets/icons/i-filter-white.svg" alt="" /> Bộ lọc
        </Button>
      </div>

      <OverlayPanel class="min-w-[620px] [&>.p-overlaypanel-content]:p-0" ref="userPanel">
        <div class="grid grid-cols-2 p-4 gap-4">
          <!-- <BaseInputSelect
            label="Thương hiệu"
            :options="listBrands"
            v-model="brand"
            option-label="label"
            placeholder="Chọn thương hiệu"
            option-value="value" /> -->
          <BaseInputSelect
            label="Điểm"
            :options="listBrands"
            v-model="brand"
            option-label="label"
            placeholder="Chọn giá"
            option-value="value" />
          <BaseInputSelect
            label="Danh mục"
            :options="listBrands"
            v-model="brand"
            option-label="label"
            placeholder="Chọn danh mục"
            option-value="value" />
          <BaseInputSelect
            label="Màu sắc"
            :options="listBrands"
            v-model="brand"
            option-label="label"
            placeholder="Chọn màu sắc"
            option-value="value" />
          <BaseInputSelect
            label="Kích thước"
            :options="listBrands"
            v-model="brand"
            option-label="label"
            placeholder="Chọn kích thước"
            option-value="value" />
          <BaseInputSelect
            label="Sắp xếp"
            :options="listBrands"
            v-model="brand"
            option-label="label"
            placeholder="Chọn sắp xếp"
            option-value="value" />
        </div>
      </OverlayPanel>
    </div>
    <div class="fr ai-c gap-6 p-2 rounded-xl bg-white">
      <div
        class="fc ai-c gap-2 p-2 rounded-lg cursor-pointer transition-all duration-300 hover:bg-gray-50 hover:-translate-y-1 hover:shadow-md border-2 border-transparent hover:border-blue-100 min-w-[100px]"
        :class="brand === '' ? 'bg-primary-20 shadow-md border-blue-200' : 'bg-gray-50'"
        @click="brand = ''">
        <img src="@/assets/images/all-product.svg" alt="Tất cả" class="w-12 h-12 object-contain rounded-lg" />
        <span class="text-sm font-medium text-center" :class="brand === '' ? 'c-primary font-semibold' : 'c-black-80'"
          >Tất cả</span
        >
      </div>
      <div
        class="fc ai-c gap-2 p-2 rounded-lg cursor-pointer transition-all duration-300 hover:bg-gray-50 hover:-translate-y-1 hover:shadow-md border-2 border-transparent hover:border-blue-100 min-w-[100px]"
        :class="brand === '005 - ALP' ? 'bg-primary-20 shadow-md border-blue-200' : 'bg-gray-50'"
        @click="brand = '005 - ALP'">
        <img src="@/assets/images/logo-5.png" alt="ALP" class="w-12 h-12 object-contain rounded-lg" />
        <span
          class="text-sm font-medium text-center"
          :class="brand === '005 - ALP' ? 'c-primary font-semibold' : 'c-black-80'"
          >ALP</span
        >
      </div>
      <div
        class="fc ai-c gap-2 p-2 rounded-lg cursor-pointer transition-all duration-300 hover:bg-gray-50 hover:-translate-y-1 hover:shadow-md border-2 border-transparent hover:border-blue-100 min-w-[100px]"
        :class="brand === '004 - Revo' ? 'bg-primary-20 shadow-md border-blue-200' : 'bg-gray-50'"
        @click="brand = '004 - Revo'">
        <img src="@/assets/images/logo-4-col.png" alt="Revo" class="w-12 h-12 object-contain rounded-lg" />
        <span
          class="text-sm font-medium text-center"
          :class="brand === '004 - Revo' ? 'c-primary font-semibold' : 'c-black-80'"
          >Revo</span
        >
      </div>
      <div
        class="fc ai-c gap-2 p-2 rounded-lg cursor-pointer transition-all duration-300 hover:bg-gray-50 hover:-translate-y-1 hover:shadow-md border-2 border-transparent hover:border-blue-100 min-w-[100px]"
        :class="brand === '001 - Dai Tan' ? 'bg-primary-20 shadow-md border-blue-200' : 'bg-gray-50'"
        @click="brand = '001 - Dai Tan'">
        <img src="@/assets/images/logo-3.png" alt="Đại Tân" class="w-12 h-12 object-contain rounded-lg" />
        <span
          class="text-sm font-medium text-center"
          :class="brand === '001 - Dai Tan' ? 'c-primary font-semibold' : 'c-black-80'"
          >Đại Tân</span
        >
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 pb-20 mt-4">
      <CardProduct v-for="product in listProduct" :key="product.id" :product="product" />
    </div>
    <EmptyState v-if="listProduct.length === 0" />
  </div>
</template>
