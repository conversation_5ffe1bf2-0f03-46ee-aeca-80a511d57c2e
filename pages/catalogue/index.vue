<script lang="ts" setup>
import { logoAvt } from '~/assets/images'
import BaseAvatar from '~/components/common/BaseAvatar.vue'
import { useAppStore } from '~/stores/app'

const posts = ref<any>([])
const appStore = useAppStore()
const { isCustomer, userType } = useRole()
const getData = async () => {
  //appStore.isLoading = true
  const url = encodeURIComponent(`catalogue:list`)
  const { data }: any = await useApi(url, {
    method: 'GET',
  })
  console.log(data.value, 'value')
  posts.value = data.value?.data.filter((el: any) => el.isEnabled == true && el.objectType == userType) || []
  appStore.isLoading = false
}

getData()
</script>

<template>
  <div class="container mx-auto">
    <div class="page-heading mb-4">Catalogue</div>
    <!-- Blog Cards Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      <nuxt-link
        v-for="post in posts"
        :key="post.id"
        :to="`/catalogue/${post.id}`"
        class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer group">
        <!-- Image -->
        <div class="relative h-48 overflow-hidden">
          <img
            :src="post.thumbnailUrl"
            :alt="post.title"
            class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300" />
        </div>

        <!-- Content -->
        <div class="p-5">
          <h3 class="text-lg font-bold c-black mb-2 line-clamp-2 group-hover:c-primary transition-colors">
            {{ post.title }}
          </h3>
          <!-- <p class="text-gray-600 text-sm mb-4 line-clamp-3">
              {{ post.excerpt }}
            </p> -->

          <!-- Author & Date -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <BaseAvatar :size="32" :url="logoAvt" />
              <div>
                <p class="text-sm font-medium text-gray-900">Admin</p>
                <p class="text-xs text-gray-500">{{ useMoment(post.createdDate) }}</p>
              </div>
            </div>
          </div>
        </div>
      </nuxt-link>
    </div>
  </div>
</template>
