<script lang="ts" setup>
import BaseAvatar from '~/components/common/BaseAvatar.vue'
import ChartDebit from '~/components/dashboard/ChartDebit.vue'
import ChartPurchaseRate from '~/components/dashboard/ChartPurchaseRate.vue'
import ChartSales from '~/components/dashboard/ChartSales.vue'
import ListSo from '~/components/dashboard/ListSo.vue'
import { useAppStore } from '~/stores/app'

const { $auth } = useNuxtApp()
const appStore = useAppStore()
const { isCustomer } = useRole()
const listCustomer = computed(() => appStore.listCustomer)
const sapCustomerId = ref($auth.user?.sapCustomerId)
const revenue = ref<any>({})
const listSO = ref([])
const infoCustomer = ref<any>({})
const getDataRevenue = async () => {
  const url = `sap_api_get?sap_api_url=ZREAD_CUSTOMER_REVENUE_SRV/RevenueSet('${sapCustomerId.value}')?$expand=MonthDetailsSet,QuarterDetailsSet,YearDetailsSet&sap_customer_id=${sapCustomerId.value}`

  const { data }: any = await useApi(url)
  if (data.value) {
    revenue.value = data.value?.d || {}
  }
}
const getDataSO = async () => {
  const url = `so_customer?sap_api_url=ZALP_SALES_ORDER_SRV/SOHeader?$filter=Customer eq '${sapCustomerId.value}'&sap_customer_id=${sapCustomerId.value}`
  const { data }: any = await useApi(url)
  listSO.value = data.value?.d?.results || []
}
const getDataCustomer = async () => {
  const url = `sap_api_get?sap_api_url=ZALP_SALES_ORDER_SRV/CustomerTypeSet?$filter=CustomerID eq '${sapCustomerId.value}'`

  const { data }: any = await useApi(url)
  console.log(data.value?.d?.results[0], 'data.value?.d?.results[0]')
  infoCustomer.value = data.value?.d?.results[0] || {}
}

const getData = async () => {
  try {
    if (sapCustomerId.value) {
      appStore.isLoading = true
      Promise.all([getDataRevenue(), getDataSO(), getDataCustomer()])
    }
  } catch (error) {
  } finally {
    appStore.isLoading = false
  }
}
getData()
watch(sapCustomerId, () => {
  getData()
})
watch(
  () => listCustomer.value,
  () => {
    if (!isCustomer && revenue.value && listCustomer.value.length > 0) {
      sapCustomerId.value = listCustomer.value[0].CustomerID || ''
    }
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <div class="fc gap-4 container mx-a">
    <div class="w-full" v-if="!isCustomer">
      <SelectCustomer v-model:customer="sapCustomerId" />
    </div>
    <div class="w-full grid grid-cols-1 xl:grid-cols-5 gap-4">
      <div class="col-span-1 xl:col-span-3 bg-white rounded">
        <ChartSales :data="revenue" />
      </div>
      <div class="col-span-1 xl:col-span-2 bg-white rounded">
        <ChartDebit :data="revenue" />
      </div>
    </div>
    <div class="w-full grid grid-cols-1 xl:grid-cols-7 gap-4">
      <div class="col-span-1 xl:col-span-2 fc gap-4">
        <div class="bg-white rounded fc gap-4 page">
          <div class="com-heading">Thông tin khách hàng</div>
          <div class="p-4 rounded-80px bg-primary-20 fr ai-c gap-4">
            <BaseAvatar class="cursor-pointer" :size="80" :url="infoCustomer?.avatar" />
            <div class="fc">
              <div class="text-black text-base font-bold">{{ infoCustomer?.FullName }}</div>
              <div class="c-title">Khách hàng</div>
            </div>
          </div>
          <div class="fc gap-2">
            <div class="fr ai-c gap-4">
              <img src="~/assets/icons/i-telephone.svg" class="w-20px h-20px" alt="" />
              <div class="c-title">
                {{ infoCustomer?.telephone_number }}
              </div>
            </div>
            <div class="fr ai-c gap-4">
              <img src="~/assets/icons/i-phone.svg" class="w-20px h-20px" alt="" />
              <div class="c-title">
                {{ infoCustomer?.phone_number }}
              </div>
            </div>
            <div class="fr ai-c gap-4">
              <img src="~/assets/icons/i-map.svg" class="w-20px h-20px" alt="" />
              <div class="c-title line-clamp-2">
                {{ infoCustomer?.street }}
              </div>
            </div>
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div class="p-2 fr ai-c gap-2 bg-primary-20 rounded">
              <img src="@/assets/icons/i-total-sales.svg" class="w-32px h-32px" alt="" />
              <div class="fc gap-1">
                <div class="text-base c-black">Tổng đơn</div>
                <div class="c-title font-bold">{{ useFormatNumber(revenue?.TotalSo) }}</div>
              </div>
            </div>
            <div class="p-2 fr ai-c gap-2 bg-primary-20 rounded">
              <img src="@/assets/icons/i-revenue.svg" class="w-32px h-32px" alt="" />
              <div class="fc">
                <div class="text-base c-black">Doanh số</div>
                <div class="c-title font-bold">{{ useFormatNumber(revenue?.Yeaamt) }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white rounded page gap-4 fc">
          <div class="com-heading">Thông tin liên hệ</div>
          <div class="fr ai-c gap-4">
            <BaseAvatar :size="64" :url="infoCustomer?.avatar" />
            <div class="fc gap-1">
              <div class="text-black text-sm font-semibold">{{ infoCustomer?.contact_name_asm }}</div>
              <div class="fr ai-c gap-4">
                <img src="@/assets/icons/i-phone.svg" class="w-20px h-20px" alt="" />
                <div class="text-black text-sm font-semibold">{{ infoCustomer?.contant_phone_asm }}</div>
              </div>
              <div class="fr ai-c gap-4">
                <img src="@/assets/icons/i-mail.svg" class="w-20px h-20px" alt="" />
                <div class="text-black text-sm font-semibold">{{ infoCustomer?.contact_email_asm }}</div>
              </div>
            </div>
          </div>
          <div class="fr ai-c gap-4">
            <BaseAvatar :size="64" :url="infoCustomer?.avatar" />
            <div class="fc gap-1">
              <div class="text-black text-sm font-semibold">{{ infoCustomer?.contact_name_sr }}</div>
              <div class="fr ai-c gap-4">
                <img src="@/assets/icons/i-phone.svg" class="w-20px h-20px" alt="" />
                <div class="text-black text-sm font-semibold">{{ infoCustomer?.contant_phone_sr }}</div>
              </div>
              <div class="fr ai-c gap-4">
                <img src="@/assets/icons/i-mail.svg" class="w-20px h-20px" alt="" />
                <div class="text-black text-sm font-semibold">{{ infoCustomer?.contact_email_sr }}</div>
              </div>
            </div>
          </div>

          <div class="fr ai-c gap-4">
            <BaseAvatar :size="64" :url="infoCustomer?.avatar" />
            <div class="fc gap-1">
              <div class="text-black text-sm font-semibold">Chăm sóc khách hàng</div>
              <div class="fr ai-c gap-4">
                <img src="@/assets/icons/i-phone.svg" class="w-20px h-20px" alt="" />
                <div class="text-black text-sm font-semibold">18006332</div>
              </div>
              <div class="fr ai-c gap-4">
                <img src="@/assets/icons/i-mail.svg" class="w-20px h-20px" alt="" />
                <div class="text-black text-sm font-semibold"><EMAIL></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-span-1 xl:col-span-5 fc gap-4">
        <div class="rounded bg-white overflow-hidden">
          <ChartPurchaseRate :data="revenue" />
        </div>
        <div class="bg-white rounded">
          <ListSo :data="listSO" />
        </div>
      </div>
    </div>
  </div>
</template>
