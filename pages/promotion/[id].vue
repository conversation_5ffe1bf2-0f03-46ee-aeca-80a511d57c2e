<script lang="ts" setup>
import { logoAvt } from '~/assets/images'
import BaseAvatar from '~/components/common/BaseAvatar.vue'
import { useAppStore } from '~/stores/app'

const post = ref<any>({})
const appStore = useAppStore()
const route = useRoute()
const router = useRouter()
const getData = async () => {
  appStore.isLoading = true
  const route = useRoute()
  const url = encodeURIComponent(`news:detail`)
  const { data }: any = await useApi(url, {
    method: 'GET',
    params: {
      id: route.params.id,
    },
  })
  console.log(data.value, 'value')
  post.value = data.value?.data || {}
  appStore.isLoading = false
}

getData()
</script>

<template>
  <div class="min-h-screen w-full">
    <div class="container max-w-5xl mx-auto overflow-auto">
      <div class="page-heading mb-4 fr ai-c gap-4">
        <img src="~/assets/icons/i-back.svg" alt="" class="cursor-pointer" @click="router.go(-1)" />
        {{ post?.title }}
      </div>
      <div class="content_html w-full" v-html="post?.content"></div>
    </div>
  </div>
</template>
