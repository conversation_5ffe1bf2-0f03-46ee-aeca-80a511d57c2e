<script setup lang="ts">
import TrackingPoint from '~/components/points/TrackingPoint.vue'

const { isCustomer, userType } = useRole()
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <!-- Header -->
      <div class="text-center mb-12">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Chính sách tích điểm</h1>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Với mỗi {{ isCustomer ? '1.000.000' : '2.000.000' }} vnđ của các thương hiệu sẽ được quy đổi thành điểm tương
          ứng như sau:
        </p>
      </div>

      <!-- Brand Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
        <!-- ALP Brand -->
        <div
          class="group relative cursor-pointer bg-gradient-to-br from-#049b66 to-#049b66 rounded-2xl shadow-xl overflow-hidden transform transition-all">
          <!-- Background Pattern -->
          <div class="absolute inset-0 bg-white opacity-10 group-hover:opacity-15 transition-opacity">
            <div
              class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full opacity-20 transform translate-x-8 -translate-y-8 transition-transform"></div>
            <div
              class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full opacity-15 transform -translate-x-4 translate-y-4 transition-transform"></div>
          </div>

          <div class="relative p-8 text-center text-white">
            <!-- Logo -->
            <div
              class="w-24 h-24 bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-opacity-30 transition-all">
              <img src="@/assets/images/logo-2.png" alt="ALP Logo" class="w-16 object-cover" />
            </div>

            <!-- Brand Name -->
            <h3 class="text-2xl font-bold mb-4">Fuji</h3>

            <!-- Points -->
            <div class="mb-6">
              <div class="text-5xl font-black mb-2 group-hover:scale-125 transition-transform">1</div>
              <p class="text-blue-100 font-medium">điểm tích lũy</p>
            </div>

            <!-- Decorative Element -->
            <div class="absolute top-6 left-6 w-3 h-3 bg-white rounded-full opacity-60"></div>
            <div class="absolute top-10 left-8 w-2 h-2 bg-white rounded-full opacity-40"></div>
          </div>
        </div>

        <!-- FUJI Brand -->
        <div
          class="group relative cursor-pointer bg-gradient-to-br from-#0976AC to-#0976AC rounded-2xl shadow-xl overflow-hidden transform transition-all">
          <!-- Background Pattern -->
          <div class="absolute inset-0 bg-white opacity-10 group-hover:opacity-15 transition-opacity">
            <div
              class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full opacity-20 transform translate-x-8 -translate-y-8 transition-transform"></div>
            <div
              class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full opacity-15 transform -translate-x-4 translate-y-4 transition-transform"></div>
          </div>

          <div class="relative p-8 text-center text-white">
            <!-- Logo -->
            <div
              class="w-24 h-24 bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-opacity-30 transition-all">
              <img src="@/assets/images/logo-3.png" alt="FUJI Logo" class="w-16 object-cover" />
            </div>

            <!-- Brand Name -->
            <h3 class="text-2xl font-bold mb-4">Đại Tân</h3>

            <!-- Points -->
            <div class="mb-6">
              <div class="text-5xl font-black mb-2 group-hover:scale-125 transition-transform">2</div>
              <p class="text-emerald-100 font-medium">điểm tích lũy</p>
            </div>

            <!-- Decorative Element -->
            <div class="absolute top-6 left-6 w-3 h-3 bg-white rounded-full opacity-60"></div>
            <div class="absolute top-10 left-8 w-2 h-2 bg-white rounded-full opacity-40"></div>
          </div>
        </div>

        <!-- DT Brand -->
        <div
          class="group relative cursor-pointer bg-gradient-to-br from-#ee343f to-#ee343f rounded-2xl shadow-xl overflow-hidden transform transition-all">
          <!-- Background Pattern -->
          <div class="absolute inset-0 bg-white opacity-10 group-hover:opacity-15 transition-opacity">
            <div
              class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full opacity-20 transform translate-x-8 -translate-y-8 transition-transform"></div>
            <div
              class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full opacity-15 transform -translate-x-4 translate-y-4 transition-transform"></div>
          </div>

          <div class="relative p-8 text-center text-white">
            <!-- Logo -->
            <div
              class="w-24 h-24 bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-opacity-30 transition-all">
              <img src="@/assets/images/logo-1.png" alt="DT Logo" class="w-16 object-cover" />
            </div>

            <!-- Brand Name -->
            <h3 class="text-2xl font-bold mb-4">YNG HUA</h3>

            <!-- Points -->
            <div class="mb-6">
              <div class="text-5xl font-black mb-2 group-hover:scale-125 transition-transform">3</div>
              <p class="text-purple-100 font-medium">điểm tích lũy</p>
            </div>

            <!-- Decorative Element -->
            <div class="absolute top-6 left-6 w-3 h-3 bg-white rounded-full opacity-60"></div>
            <div class="absolute top-10 left-8 w-2 h-2 bg-white rounded-full opacity-40"></div>
          </div>
        </div>

        <!-- YNG Brand -->
        <div
          class="group relative cursor-pointer bg-gradient-to-br from-#2E2F28 to-#2E2F28 rounded-2xl shadow-xl overflow-hidden transform transition-all">
          <!-- Background Pattern -->
          <div class="absolute inset-0 bg-white opacity-10 group-hover:opacity-15 transition-opacity">
            <div
              class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full opacity-20 transform translate-x-8 -translate-y-8 transition-transform"></div>
            <div
              class="absolute bottom-4 left-4 w-24 h-24 bg-white rounded-full opacity-15 transform -translate-x-4 translate-y-4 transition-transform"></div>
          </div>

          <div class="relative p-8 text-center text-white">
            <!-- Logo -->
            <div
              class="w-24 h-24 bg-white bg-opacity-20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:bg-opacity-30 transition-all">
              <img src="@/assets/images/logo-4.jpg" alt="YNG Logo" class="w-16 object-cover" />
            </div>

            <!-- Brand Name -->
            <h3 class="text-2xl font-bold mb-4">REVO</h3>

            <!-- Points -->
            <div class="mb-6">
              <div class="text-5xl font-black mb-2 group-hover:scale-125 transition-transform">4</div>
              <p class="text-orange-100 font-medium">điểm tích lũy</p>
            </div>

            <!-- Decorative Element -->
            <div class="absolute top-6 left-6 w-3 h-3 bg-white rounded-full opacity-60"></div>
            <div class="absolute top-10 left-8 w-2 h-2 bg-white rounded-full opacity-40"></div>
          </div>
        </div>
      </div>

      <div class="mt-10">
        <TrackingPoint />
      </div>
    </div>
  </div>
</template>
