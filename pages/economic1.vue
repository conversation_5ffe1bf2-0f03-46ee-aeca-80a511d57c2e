<script lang="ts" setup>
import { listSegmentation } from '~/constants'
import { useAppStore } from '~/stores/app'

const appStore = useAppStore()

const { $auth: auth } = useNuxtApp()
const isLoading = ref(false)
const listData = ref<any>([])
const listCustomer = computed(() => appStore.listCustomer)
const { isCustomer } = useRole()
const sapCustomerId = ref(auth.user?.sapCustomerId)
const segmentation = ref('9999')
const info = ref<any>({})
const listReport = ref<any>([])
const getCredit = async () => {
  const url = `ZREAD_CREDIT_LIMIT_SRV/CreditLimitItemSet?$filter=Partner eq '${sapCustomerId.value}' and CreditSgmnt eq '${segmentation.value}'&format=json`

  const { data }: any = await useApi(`sap_api_get`, {
    method: 'GET',
    params: {
      sap_api_url: url,
    },
  })
  info.value = data.value?.d?.results[0] || {}
}
const getReport = async () => {
  const url = `ZREAD_CREDIT_LIMIT_SRV/OpenItemBalanceSet?$filter=Partner eq '${sapCustomerId.value}' and CreditSgmnt eq '${segmentation.value}'&format=json`

  const { data }: any = await useApi(`sap_api_get`, {
    method: 'GET',
    params: {
      sap_api_url: url,
    },
  })
  listReport.value = data.value?.d?.results || []
}
const getData = async () => {
  try {
    isLoading.value = true
    await Promise.all([getCredit(), getReport()])
  } catch (error) {
  } finally {
    isLoading.value = false
  }
}

if (sapCustomerId.value) {
  getData()
}
watch(
  () => listCustomer.value,
  () => {
    if (!isCustomer && info.value && listCustomer.value.length > 0) {
      sapCustomerId.value = listCustomer.value[0].CustomerID || ''
      getData()
    }
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <div class="container mx-a fc gap-4">
    <div class="page-heading">Hạn mức công nợ</div>
    <div class="w-full grid grid-cols-3 gap-4" v-if="!isCustomer">
      <SelectCustomer v-model:customer="sapCustomerId" />
      <BaseInputSelect
        v-model="segmentation"
        placeholder="Chọn"
        :options="listSegmentation"
        option-label="label"
        label="Phân đoạn tín dụng"
        option-value="value" />
      <div class="fc">
        <div class="flex-1"></div>
        <Button label="Tìm kiếm" class="mb-1" @click="getData" :loading="isLoading" />
      </div>
    </div>
    <div class="bg-white p-4 rounded fc gap-4">
      <div class="com-heading">Hạn mức công nợ</div>
      <div class="grid grid-cols-7">
        <div class="col-span-2 p-4 gap-4 fr ai-c bg-#75a0b9 rounded-tl rounded-bl">
          <div class="text-base font-normal c-white opacity-60 whitespace-nowrap">Mã khách hàng:</div>
          <div class="text-base c-white font-bold line-clamp-1">{{ sapCustomerId }}</div>
        </div>
        <div
          class="col-span-3 p-4 gap-4 fr ai-c bg-#75a0b9 border-1px border-solid border-white border-t-none border-b-none">
          <div class="text-base font-normal c-white opacity-60 whitespace-nowrap">Tên khách hàng:</div>
          <div class="text-base c-white font-bold line-clamp-2">{{ info?.Descrip }}</div>
        </div>
        <div class="col-span-2 p-4 gap-4 fr ai-c bg-#75a0b9 rounded-tr rounded-br">
          <div class="text-base font-normal c-white opacity-60 whitespace-nowrap">Phân đoạn tín dụng:</div>
          <div class="text-base c-white font-bold line-clamp-2">{{ info?.CreditSgmntTxt }}</div>
        </div>
      </div>
      <div class="grid grid-cols-2">
        <!-- <div class="bg-red h-100">qqq</div> -->
        <DataTable
          :value="listReport"
          dataKey="_id"
          class="w-full col-span-2"
          rowHover
          scrollable
          scroll-height="calc(100vh - 350px)"
          :rows="1000"
          :loading="isLoading">
          <Column
            field="Opsum"
            header="Tổng hạn mức công nợ (VNĐ)"
            style="min-width: 200px"
            :frozen="true"
            alignFrozen="left">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Opsum) }}
            </template>
          </Column>
          <Column
            field="Opsum"
            header="Tổng công nợ đã dùng (VNĐ)"
            style="min-width: 200px"
            :frozen="true"
            alignFrozen="left">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Opsum) }}
            </template>
          </Column>
          <Column field="Rast1" header="Hạn mức công nợ còn lại(VNĐ)" style="min-width: 200px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Rast1) }}
            </template>
          </Column>
          <Column field="Rast2" header="Giá trị đơn hàng đã xác nhận và đang chờ giao (VNĐ)" style="min-width: 200px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Rast2) }}
            </template>
          </Column>
          <Column
            field="Rast3"
            header="Hạn mức công nợ còn lại sau khi đã trừ xác nhận và đang chờ (VNĐ):"
            style="min-width: 200px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Rast4) }}
            </template>
          </Column>
          <Column
            field="Rast4"
            header=" Công nợ vượt hạn mức sau khi đã trừ xác nhận và đang chờ (VNĐ):"
            style="min-width: 200px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Rast4) }}
            </template>
          </Column>

          <template #empty>
            <EmptyState />
          </template>
        </DataTable>
      </div>
    </div>
    <div class="bg-white fc gap-4 p-4 rounded w-full">
      <div class="com-heading">Báo cáo công nợ</div>

      <div class="grid grid-cols-2">
        <!-- <div class="bg-red h-100">qqq</div> -->
        <DataTable
          :value="listReport"
          dataKey="_id"
          class="w-full col-span-2"
          rowHover
          scrollable
          scroll-height="calc(100vh - 350px)"
          :rows="1000"
          :loading="isLoading">
          <Column header="#" :frozen="true" alignFrozen="left">
            <template #body="slotProps">
              {{ slotProps.index + 1 }}
            </template>
          </Column>

          <Column field="Opsum" header="Tổng nợ phải thu" style="min-width: 200px" :frozen="true" alignFrozen="left">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Opsum) }}
            </template>
          </Column>
          <Column field="moneyType" header="Loại tiền" style="min-width: 150px"> </Column>
          <Column field="Rast1" header="Trong thời hạn thanh toán" style="min-width: 150px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Rast1) }}
            </template>
          </Column>
          <Column field="Rast2" header="Quá hạn <= 30 ngày" style="min-width: 100px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Rast2) }}
            </template>
          </Column>
          <Column field="Rast3" header="Quá hạn 31-60 ngày" style="min-width: 100px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Rast4) }}
            </template>
          </Column>
          <Column field="Rast4" header="Quá hạn 61-90 ngày" style="min-width: 100px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Rast4) }}
            </template>
          </Column>
          <Column field="Rast5" header="Quá hạn 91-180 ngày" style="min-width: 100px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Rast5) }}
            </template>
          </Column>
          <Column field="Rast6" header="Quá hạn >= 181 ngày" style="min-width: 100px">
            <template #body="slotProps">
              {{ useFormatNumber(slotProps.data.Rast6) }}
            </template>
          </Column>

          <template #empty>
            <EmptyState />
          </template>
        </DataTable>
      </div>
    </div>
  </div>
</template>
