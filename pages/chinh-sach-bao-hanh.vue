<script lang="ts" setup>
import { logoAvt } from '~/assets/images'
import BaseAvatar from '~/components/common/BaseAvatar.vue'
import { useAppStore } from '~/stores/app'
import PDF from 'pdf-vue3'

const pricing = ref<any>({})
const appStore = useAppStore()
const route = useRoute()
const router = useRouter()
</script>

<template>
  <div class="container mx-auto">
    <div class="page-heading mb-4 fr ai-c gap-4">
      <img src="~/assets/icons/i-back.svg" alt="" class="cursor-pointer" @click="router.go(-1)" />
      Chính sách bảo hành
    </div>
    <PDF class="h-fit" src="./chinh-sach-bao-hanh.pdf" :showPageTooltip="false" :showProgress="true" />
  </div>
</template>

<style scoped lang="scss">
:deep(.pdf-vue3-scroller) {
  overflow: auto !important;
  height: fit-content !important;
  max-height: initial !important;
}
</style>
