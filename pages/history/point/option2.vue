<script lang="ts" setup>
import { logoDefault } from '~/assets/images'
import { optionStatus, statusColor, status } from '~/constants'
import { useAppStore } from '~/stores/app'
const { $auth: auth, $dayjs: dayjs } = useNuxtApp()
const route = useRoute()

const query = ref({
  search: (route.query.search as string) || '',
  status: (Number(route.query.status) as number) || '',
  orderType: (route.query.orderType as string) || '',
  username: auth?.user?.username || undefined,
  sapCustomerId: auth?.user?.sapCustomerId || null,
})
const appStore = useAppStore()
const { isCustomer } = useRole()
const listOrder = ref<any>([])
const listType = [
  {
    name: 'Tất cả',
    value: '',
  },
  {
    name: 'Đơn bán',
    value: 'ZOR2',
  },
  {
    name: 'Đơn trả',
    value: 'ZRE1',
  },
  {
    name: 'Đ<PERSON><PERSON> xuất đổi',
    value: 'ZSDH',
  },
  {
    name: 'Đ<PERSON><PERSON> nhập đổi',
    value: 'ZSDH',
  },
  {
    name: '<PERSON><PERSON>i điểm',
    value: 'ZFOC',
  },
]

const getData = async () => {
  appStore.isLoading = true
  const url = query.value.sapCustomerId
    ? `ZALP_SALES_ORDER_SRV/SOHeader?$filter=Customer eq '${query.value.sapCustomerId}'&sap_customer_id=${query.value.sapCustomerId}`
    : `ZALP_SALES_ORDER_SRV/SOHeader?$filter=UserName eq '${query.value.username}'`
  const { data }: any = await useApi(`sap_api_get`, {
    method: 'GET',
    params: {
      sap_api_url: url,
    },
  })
  listOrder.value = data.value?.d?.results || []
  appStore.isLoading = false
}
// getData()

const dataFilter: any = computed(() => {
  //   const arr = listOrder.value.filter((item: any) => {
  //     return (
  //       (query.value.search ? item.OrderNumber.toLowerCase().includes(query.value.search.toLowerCase()) : true) &&
  //       (query.value.orderType ? item.OrderType == query.value.orderType : true) &&
  //       (query.value.status ? item.Status == query.value.status : true)
  //     )
  //   })
  const arr = listOrder.value

  return (
    arr.sort((a: any, b: any) => {
      return dayjs(b.OrderDate).valueOf() - dayjs(a.OrderDate).valueOf()
    }) || []
  )
})
watchDebounced(
  () => query.value.search,
  (newValue) => {
    useQueryURL(query.value)
  },
  {
    debounce: 300,
    maxWait: 4000,
  },
)
watchDebounced(
  () => query.value.status,
  (newValue) => {
    useQueryURL(query.value)
  },
  {
    debounce: 300,
    maxWait: 1000,
  },
)
watchDebounced(
  () => query.value.sapCustomerId,
  (newValue) => {
    getData()
  },
  {
    debounce: 300,
    maxWait: 1000,
  },
)

// Add computed property for status counts
const statusCounts = computed(() => {
  const counts: any = {}

  // Initialize counts for all status values
  optionStatus.forEach((item: any) => {
    counts[item.value] = 0
  })

  // Count actual orders
  listOrder.value.forEach((item: any) => {
    if (counts.hasOwnProperty(item.Status)) {
      counts[item.Status]++
    }
  })

  // Add total count for "All" status
  counts['all'] = listOrder.value.length

  return counts
})

// Sample data for demonstration
const sampleOrders = [
  {
    OrderNumber: 'P00000001',
    OrderType: 'ZFOC',
    OrderDate: '2024-01-15',
    DeliveryDate: '2024-01-20',
    CustomerName: 'Công ty ABC',
    TotalPrice: '150000',
    Status: '20',
    StatusText: 'Chờ xác nhận',
    Items: [
      {
        Material: '20-FJ7053T00-600',
        MaterialText: 'PML80B0614_8M24148_400_KB NGANG DƯỚI',
        OrderQuantity: '2',
        OrderUnit: 'BO',
        TotalPrice: '80000',
      },
      {
        Material: '20-FJ7053T00-600',
        MaterialText: 'PR14512100_8M24148_600_KB CĐ-CS',
        OrderQuantity: '1',
        OrderUnit: 'KG',
        TotalPrice: '70000',
      },
    ],
  },
  {
    OrderNumber: 'P00000002',
    OrderType: 'ZFOC',
    OrderDate: '2024-01-16',
    DeliveryDate: '2024-01-22',
    CustomerName: 'Công ty XYZ',
    TotalPrice: '250000',
    Status: '50',
    StatusText: 'Hoàn thành',
    Items: [
      {
        Material: '20-FJ7053T00-600',
        MaterialText: 'PR14514100_8M24147_430_T.GIAN CỐ ĐỊNH',
        OrderQuantity: '3',
        OrderUnit: 'BO',
        TotalPrice: '150000',
      },
      {
        Material: '20-FJ7053T00-600',
        MaterialText: 'SC9064705_7L10045_300_THANH CHE CÁNH',
        OrderQuantity: '2',
        OrderUnit: 'CAY',
        TotalPrice: '100000',
      },
    ],
  },
]

// Use sample data instead of API data
listOrder.value = sampleOrders
</script>

<template>
  <div class="min-h-80vh container mx-a">
    <div class="w-full mx-a fc gap-4">
      <div class="fr jc-sb ai-c">
        <div class="page-heading">Danh sách đơn hàng</div>
      </div>
      <div class="grid grid-cols-4 gap-4 overflow-hidden">
        <SelectCustomer :showClear="true" v-model:customer="query.sapCustomerId" v-if="!isCustomer" />
        <BaseInputText
          label="Mã đơn hàng"
          name="search"
          class="min-w-300px"
          placeholder="Nhập để tìm kiếm"
          v-model="query.search" />
        <BaseInputSelect
          label="Sắp xếp"
          v-model="query.orderType"
          :options="listType"
          name="orderType"
          option-label="name"
          option-value="value"
          class="min-w-300px"
          placeholder="Chọn sắp xếp" />
        <div class="fc jc-fe pb-1px">
          <Button label="Xuất danh sách" severity="primary" class="h-fit"></Button>
        </div>
        <!-- <BaseInputSelect
          label="Trạng thái"
          v-model="query.status"
          :options="listStatus"
          name="status"
          option-label="name"
          option-value="value"
          class="min-w-300px"
          placeholder="Chọn status" /> -->
      </div>
      <div class="grid grid-cols-1">
        <div class="bg-white rounded p-4 fc gap-4">
          <div class="fr ai-c w-full gap-2 overflow-x-scroll py-2 px-2 hide-scroll-bar">
            <!-- All status tab -->
            <div
              class="fr ai-c gap-2 bg-gray-100 rounded-lg px-3 py-2 cursor-pointer hover:opacity-80 transition-opacity w-160px min-w-160px"
              @click="query.status = ''"
              :class="{ 'ring-2': query.status === '' }">
              <img src="@/assets/icons/i-status-all.svg" class="w-32px h-32px" alt="" />
              <div class="fc">
                <div class="text-xs font-semibold">Tất cả</div>
                <div class="text-base font-bold text-gray-600">
                  {{ statusCounts['all'] || 0 }}
                </div>
              </div>
            </div>
            <div
              v-for="statusItem in optionStatus"
              :key="statusItem.value"
              class="fr ai-c gap-2 rounded-lg px-3 py-2 cursor-pointer hover:opacity-80 transition-opacity w-160px min-w-160px"
              :style="`background-color: ${statusColor[statusItem.value]}1a`"
              @click="query.status = query.status === statusItem.value ? '' : statusItem.value"
              :class="{ 'ring-2': query.status === statusItem.value }">
              <img :src="statusItem.icon" class="w-32px h-32px" alt="" />
              <div class="fc">
                <div class="text-xs font-semibold">{{ statusItem.name }}</div>
                <div class="text-base font-bold" :style="`color: ${statusColor[statusItem.value]}`">
                  {{ statusCounts[statusItem.value] || 0 }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="com-heading my-4">Danh sách đơn hàng</div>

        <div class="bg-gray-50 w-full">
          <!-- Custom Order List -->
          <div class="fc gap-4" v-if="dataFilter.length > 0">
            <div
              v-for="(order, index) in dataFilter"
              :key="order.OrderNumber"
              class="border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-all duration-200 bg-white shadow">
              <!-- Order Header -->
              <div class="fr jc-sb ai-c mb-3 pb-3 border-b border-gray-100">
                <div class="fr ai-c gap-4">
                  <div class="text-lg font-bold text-primary">
                    <nuxt-link
                      :to="
                        order.OrderType == 'ZFOC'
                          ? `/history/point/${order.OrderNumber}`
                          : `/history/default/${order.OrderNumber}`
                      ">
                      {{ order.OrderNumber }}
                    </nuxt-link>
                  </div>

                  <div
                    class="px-2 py-1 text-xs rounded text-white shadow-sm"
                    :style="`background-color: ${statusColor[order.Status]}`">
                    {{ order.StatusText }}
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-lg font-bold">
                    {{ useFormatNumber(order.TotalPrice) }}
                    {{ order.OrderType === 'ZFOC' ? 'điểm' : 'VNĐ' }}
                  </div>
                </div>
              </div>

              <!-- Order Info -->
              <div class="grid grid-cols-3 gap-4 mb-4 bg-blue-50 rounded-lg">
                <div class="p-3 border border-gray-100">
                  <div class="text-xs text-gray-500">Ngày đặt</div>
                  <div class="font-medium">{{ useMoment(order.OrderDate, 'YYYY-MM-DD') }}</div>
                </div>
                <div class="p-3 border border-gray-100">
                  <div class="text-xs text-gray-500">Ngày giao</div>
                  <div class="font-medium">{{ useMoment(order.DeliveryDate, 'YYYY-MM-DD') }}</div>
                </div>
                <div class="p-3 border border-gray-100">
                  <div class="text-xs text-gray-500">Nơi giao</div>
                  <div class="font-medium">{{ order.CustomerName }}</div>
                </div>
              </div>

              <!-- Order Items -->
              <div class="fc gap-3">
                <div class="text-sm font-semibold text-gray-700 mb-2">Chi tiết sản phẩm:</div>
                <div
                  v-for="item in order.Items"
                  :key="item.Material"
                  class="fr ai-c gap-4 py-3 px-4 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <!-- Product Image -->
                  <div class="flex-shrink-0">
                    <img
                      :src="item.image || logoDefault"
                      :alt="item.MaterialText"
                      class="w-16 h-16 object-cover rounded-lg border border-gray-200" />
                  </div>

                  <!-- Product Info -->
                  <div class="flex-1 min-w-0">
                    <div class="font-medium text-gray-800 line-clamp-2">{{ item.MaterialText }}</div>
                    <div class="text-sm text-gray-500 mt-1">{{ item.Material }}</div>
                  </div>

                  <!-- Quantity Display -->
                  <div class="fr ai-c gap-6">
                    <div class="text-center">
                      <div class="text-xs text-gray-500 font-medium">Số lượng</div>
                      <div class="font-semibold text-gray-800">
                        {{ useFormatNumber(item.OrderQuantity) }}
                      </div>
                    </div>

                    <!-- Price -->
                    <div class="text-right">
                      <div class="text-xs text-gray-500 font-medium">Số điểm</div>
                      <div class="font-semibold text-gray-800">
                        {{ useFormatNumber(item.TotalPrice) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else class="text-center py-12">
            <EmptyState />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
