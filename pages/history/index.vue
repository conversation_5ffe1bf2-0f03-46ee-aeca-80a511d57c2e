<script lang="ts" setup>
import { optionStatus, statusColor, status } from '~/constants'
import { useAppStore } from '~/stores/app'
const { $auth: auth, $dayjs: dayjs } = useNuxtApp()
const route = useRoute()

const query = ref({
  search: (route.query.search as string) || '',
  status: (Number(route.query.status) as number) || '',
  orderType: (route.query.orderType as string) || '',
  username: auth?.user?.username || undefined,
  sapCustomerId: auth?.user?.sapCustomerId || null,
})
const appStore = useAppStore()
const { isCustomer } = useRole()
const listOrder = ref([])
const listType = [
  {
    name: 'Tất cả',
    value: '',
  },
  {
    name: 'Đơn bán',
    value: 'ZOR2',
  },
  {
    name: 'Đơn tr<PERSON>',
    value: 'ZRE1',
  },
  {
    name: 'Đ<PERSON><PERSON> xuất đổi',
    value: 'ZSDH',
  },
  {
    name: 'Đ<PERSON>n nhập đổi',
    value: 'ZSDH',
  },
  {
    name: 'Đ<PERSON><PERSON> điểm',
    value: 'ZF<PERSON>',
  },
]

const getData = async () => {
  appStore.isLoading = true
  const url = query.value.sapCustomerId
    ? `ZALP_SALES_ORDER_SRV/SOHeader?$filter=Customer eq '${query.value.sapCustomerId}'&sap_customer_id=${query.value.sapCustomerId}`
    : `ZALP_SALES_ORDER_SRV/SOHeader?$filter=UserName eq '${query.value.username}'`
  const { data }: any = await useApi(`sap_api_get`, {
    method: 'GET',
    params: {
      sap_api_url: url,
    },
  })
  listOrder.value = data.value?.d?.results || []
  appStore.isLoading = false
}
getData()

const dataFilter: any = computed(() => {
  const arr = listOrder.value.filter((item: any) => {
    return (
      (query.value.search ? item.OrderNumber.toLowerCase().includes(query.value.search.toLowerCase()) : true) &&
      (query.value.orderType ? item.OrderType == query.value.orderType : true) &&
      (query.value.status ? item.Status == query.value.status : true)
    )
  })

  return (
    arr.sort((a: any, b: any) => {
      return dayjs(b.OrderDate).valueOf() - dayjs(a.OrderDate).valueOf()
    }) || []
  )
})
watchDebounced(
  () => query.value.search,
  (newValue) => {
    useQueryURL(query.value)
  },
  {
    debounce: 300,
    maxWait: 4000,
  },
)
watchDebounced(
  () => query.value.status,
  (newValue) => {
    useQueryURL(query.value)
  },
  {
    debounce: 300,
    maxWait: 1000,
  },
)
watchDebounced(
  () => query.value.sapCustomerId,
  (newValue) => {
    getData()
  },
  {
    debounce: 300,
    maxWait: 1000,
  },
)

// Add computed property for status counts
const statusCounts = computed(() => {
  const counts: any = {}

  // Initialize counts for all status values
  optionStatus.forEach((item: any) => {
    counts[item.value] = 0
  })

  // Count actual orders
  listOrder.value.forEach((item: any) => {
    if (counts.hasOwnProperty(item.Status)) {
      counts[item.Status]++
    }
  })

  // Add total count for "All" status
  counts['all'] = listOrder.value.length

  return counts
})
</script>

<template>
  <div class="min-h-80vh container mx-a">
    <div class="w-full mx-a fc gap-4">
      <div class="fr jc-sb ai-c">
        <div class="page-heading">Danh sách đơn hàng</div>
      </div>
      <div class="grid grid-cols-4 gap-4 overflow-hidden">
        <SelectCustomer :showClear="true" v-model:customer="query.sapCustomerId" v-if="!isCustomer" />
        <BaseInputText
          label="Mã đơn hàng"
          name="search"
          class="min-w-300px"
          placeholder="Nhập để tìm kiếm"
          v-model="query.search" />
        <BaseInputSelect
          label="Loại đơn hàng"
          v-model="query.orderType"
          :options="listType"
          name="orderType"
          option-label="name"
          option-value="value"
          class="min-w-300px"
          placeholder="Chọn loại đơn hàng" />
        <div class="fc jc-fe pb-1px">
          <Button label="Xuất danh sách" severity="primary" class="h-fit"></Button>
        </div>
        <!-- <BaseInputSelect
          label="Trạng thái"
          v-model="query.status"
          :options="listStatus"
          name="status"
          option-label="name"
          option-value="value"
          class="min-w-300px"
          placeholder="Chọn status" /> -->
      </div>
      <div class="bg-white rounded p-4 fc gap-4 grid grid-cols-1">
        <div class="fr ai-c w-full gap-2 overflow-x-scroll py-2 px-2 hide-scroll-bar">
          <!-- All status tab -->
          <div
            class="fr ai-c gap-2 bg-gray-100 rounded-lg px-3 py-2 cursor-pointer hover:opacity-80 transition-opacity w-160px min-w-160px"
            @click="query.status = ''"
            :class="{ 'ring-2': query.status === '' }">
            <img src="@/assets/icons/i-status-all.svg" class="w-32px h-32px" alt="" />
            <div class="fc">
              <div class="text-xs font-semibold">Tất cả</div>
              <div class="text-base font-bold text-gray-600">
                {{ statusCounts['all'] || 0 }}
              </div>
            </div>
          </div>
          <div
            v-for="statusItem in optionStatus"
            :key="statusItem.value"
            class="fr ai-c gap-2 rounded-lg px-3 py-2 cursor-pointer hover:opacity-80 transition-opacity w-160px min-w-160px"
            :style="`background-color: ${statusColor[statusItem.value]}1a`"
            @click="query.status = query.status === statusItem.value ? '' : statusItem.value"
            :class="{ 'ring-2': query.status === statusItem.value }">
            <img :src="statusItem.icon" class="w-32px h-32px" alt="" />
            <div class="fc">
              <div class="text-xs font-semibold">{{ statusItem.name }}</div>
              <div class="text-base font-bold" :style="`color: ${statusColor[statusItem.value]}`">
                {{ statusCounts[statusItem.value] || 0 }}
              </div>
            </div>
          </div>
        </div>
        <div class="com-heading">Danh sách đơn hàng</div>
        <DataTable
          :value="dataFilter"
          dataKey="_id"
          rowHover
          scrollable
          :rows="1000"
          :loading="appStore.isLoading"
          scroll-height="calc(100vh - 300px)">
          <Column header="#">
            <template #body="slotProps">
              {{ slotProps.index + 1 }}
            </template>
          </Column>
          <Column field="OrderNumber" header="Mã đơn hàng" class="min-w-150px">
            <template #body="slotProps">
              <nuxt-link
                :to="
                  slotProps.data.OrderType == 'ZFOC'
                    ? `/history/point/${slotProps.data.OrderNumber}`
                    : `/history/default/${slotProps.data.OrderNumber}`
                ">
                {{ slotProps.data.OrderNumber }}
              </nuxt-link>
            </template>
          </Column>
          <Column field="OrderType" header="Loại đơn hàng" class="min-w-150px">
            <template #body="slotProps">
              {{ listType.find((item) => item.value === slotProps.data.OrderType)?.name }}
            </template>
          </Column>
          <Column field="CreatedDate" header=" Ngày đặt" class="min-w-150px">
            <template #body="slotProps">
              <span class="text-base font-normal line-clamp-1">
                {{ useMoment(slotProps.data.OrderDate, 'YYYY-MM-DD') }}
              </span>
            </template>
          </Column>
          <Column field="DeliveryDate" header=" Ngày giao" class="min-w-150px">
            <template #body="slotProps">
              <span class="text-base font-normal line-clamp-1">
                {{ useMoment(slotProps.data.DeliveryDate, 'YYYY-MM-DD') }}
              </span>
            </template>
          </Column>
          <Column field="CustomerName" header="Nơi giao" class="min-w-300px">
            <template #body="slotProps">
              <span class="text-base font-normal line-clamp-1">
                {{ slotProps.data.CustomerName }}
              </span>
            </template>
          </Column>
          <Column field="TotalPrice" header="Tổng" class="min-w-200px">
            <template #body="slotProps">
              <span class="text-base font-normal line-clamp-1">
                {{ useFormatNumber(slotProps.data.TotalPrice) }}
                {{ slotProps.data.OrderType === 'ZFOC' ? 'điểm' : 'VNĐ' }}
              </span>
            </template>
          </Column>
          <Column field="StatusText" header="Tình trạng" class="min-w-200px">
            <template #body="slotProps">
              <span class="text-base font-normal line-clamp-1" :style="`color: ${statusColor[slotProps.data.Status]}`">
                {{ slotProps.data.StatusText }}
              </span>
            </template>
          </Column>

          <template #empty>
            <EmptyState />
          </template>
        </DataTable>
      </div>
    </div>
  </div>
</template>
