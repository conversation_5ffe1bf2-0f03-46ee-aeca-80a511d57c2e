<script setup lang="ts">
import { useForm } from 'vee-validate'
import { statusColor } from '~/constants'
import { useAppStore } from '~/stores/app'
import useFormatNumber from '~/composables/useFormatNumber'
const router = useRouter()
const appStore = useAppStore()
const showSuccessModal = ref(false)
const { $auth: auth }: any = useNuxtApp()
const { isCustomer } = useRole()
const sapCustomerId = ref(auth.user?.sapCustomerId || '')
const username = ref(auth.user?.username || '')
const listCart = ref<any>([])
const listAddress = ref<any>([])
const toast = useToast()
const route = useRoute()
const { $dayjs: dayjs } = useNuxtApp()
const result = ref<any>({})

const orderForm = ref<any>({
  fullname: '',
  sapCustomerId: sapCustomerId.value,
  shipToParty: '',
  shipToName: '',
  note: '',
  address: '',
})
const isLoading = ref(false)
const calculateTotal = computed(() => {
  return listCart.value.reduce((total: number, item: any) => total + useParseNumber(item?.netValue), 0)
})
const calculateTax = computed(() => {
  return listCart.value.reduce((total: number, item: any) => total + useParseNumber(item?.taxAmount), 0)
})
const calculateTotalTax = computed(() => {
  return listCart.value.reduce(
    (total: number, item: any) => total + useParseNumber(item?.netValue) + useParseNumber(item?.taxAmount),
    0,
  )
})
const { handleSubmit, resetForm } = useForm()
const onSubmit = handleSubmit(async () => {
  try {
    isLoading.value = true
    const body = {
      OrderNumber: '',
      SAPOrderNumber: '',
      // Customer: this.form.customer || '',
      UserName: username.value,
      CustomerName: '',
      OrderDate: `/Date(${dayjs().valueOf()})/`,
      DeliveryDate: `/Date(${dayjs().valueOf()})/`,
      ShipToParty: orderForm.value.sapCustomerId,
      ShipToName: orderForm.value.shipToName,
      ShipToAddress: '',
      // SalesOrg: this.form.SalesOrg,
      // DistributionChannel: this.form.DistributionChannel,
      // Division: this.form.Division,
      OrderType: 'ZFOC',
      Status: '20',
      StatusText: '',
      TotalPrice: '0.00',
      Currency: 'VND',
      TotalWeight: '0.00',
      WeightUnit: 'KG',
      Note: orderForm.value.note,
      RejectReason: '',
      FeUserId: auth.user.id.toString(),
      CreatedDate: `/Date(${dayjs().valueOf()})/`,
      CreatedBy: username.value,
      ChangedDate: `/Date(${dayjs().valueOf()})/`,
      ChangedBy: '',
      Address: orderForm.value.address,
      Phone: orderForm.value.phone,
      Items: listCart.value.map((el: any, index: number) => {
        const data = {
          // SalesMaterial: el?.SalesMaterial?.toString() || '',
          Material: el?.Materialnumber || '',
          // OrderUnit: el?.Dvt?.toString() || '',
          // Status: el?.Status?.toString() || '',
          // MaterialDescription: el?.MaterialDescription?.toString() || '',
          OrderQuantity: el?.quantity?.toString() || '',
          // Points: el?.Points?.toString() || '',
          // VariantType: el?.VariantType?.toString() || '',
          // VariantValue: el?.VariantValue?.toString() || '',
          // PortalDescription: el?.PortalDescription?.toString() || '',
          // ColorCode: el?.ColorCode?.toString() || '',
          // Filename: el?.Filename?.toString() || '',
          // Hasattachment: el?.Hasattachment?.toString() || '',
        }
        return data
      }),
    }
    const { data }: any = await useApi(`so_create?sap_api_url=ZALP_SALES_ORDER_SRV/SOHeader`, {
      method: 'POST',
      body,
    })
    console.log(data.value, 'submit')
    if (data.value?.error) {
      toast.add({ severity: 'error', summary: 'Thông báo', detail: 'Đã xảy ra sự cố khi đặt hàng', life: 3000 })
    }
    if (data.value?.d) {
      // toast.add({
      //   severity: 'success',
      //   summary: 'Thông báo',
      //   detail: `Đơn hàng ${data.value?.d?.OrderNumber} đã đặt thành công`,
      //   life: 3000,
      // })
      appStore.listCart = []
      result.value = data.value?.d
      showSuccessModal.value = true
    }
    isLoading.value = false
  } catch (error) {
  } finally {
    isLoading.value = false
  }
})
const getData = async () => {
  appStore.isLoading = true
  const { data }: any = await useApi(`sap_api_get`, {
    method: 'GET',
    params: {
      sap_api_url: isCustomer
        ? `ZALP_SALES_ORDER_SRV/SOHeader('${route.params.id}')?$expand=Items&sap_customer_id=${sapCustomerId.value}`
        : `ZALP_SALES_ORDER_SRV/SOHeader('${route.params.id}')?$expand=Items&sap_customer_id=${sapCustomerId.value}`,
    },
  })
  listCart.value =
    data.value?.d?.Items?.results?.map((el: any) => {
      el.quantity = useParseNumber(el.OrderQuantity)
      el.MaterialDescription = el.MaterialText || ''
      el.netPrice = Number(el.TotalPrice) / Number(el.OrderQuantity) || ''
      el.netValue = el.TotalPrice || ''
      el.taxAmount = el.TaxAmount || ''
      el.Points = useParseNumber(el.pointx1)
      return el
    }) || []
  orderForm.value.address = data.value?.d?.ShipToAddress || data.value?.d?.Address || ''
  orderForm.value.phone = data.value?.d?.Phone || ''
  orderForm.value.shipToParty = data.value?.d?.ShipToAddress || data.value?.d?.Address || ''
  orderForm.value.shipToName = data.value?.d?.ShipToName || data.value?.d?.CustomerName || ''
  orderForm.value.sapCustomerId = data.value?.d?.Customer || ''
  orderForm.value.note = data.value?.d?.Note || ''
  orderForm.value.statusText = data.value?.d?.StatusText || ''
  orderForm.value.status = data.value?.d?.Status || ''
  appStore.isLoading = false
}
const continueShopping = () => {
  router.push('/convert-point')
}
const viewOrderDetails = () => {
  showSuccessModal.value = false
  router.push(`/history/point/${result.value?.OrderNumber}`)
}
getData()
watch(
  () => orderForm.value.sapCustomerId,
  (val) => {
    const item: any = listAddress.value.find((item: any) => item.Customer === val)
    if (item) {
      orderForm.value.shipToParty = item.Address
      orderForm.value.shipToName = item.FullName
    }
  },
)
</script>

<template>
  <div class="container mx-auto">
    <!-- Header -->
    <div class="mb-4">
      <h1 class="page-heading">Đơn hàng #{{ route.params.id }}</h1>
      <p class="text-gray-600 mt-2">{{ listCart.length }} sản phẩm trong đơn hàng</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Cart Items -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6">
            <h2 class="com-heading mb-6">Sản phẩm đã chọn</h2>

            <!-- Empty Cart -->
            <div v-if="listCart.length === 0" class="text-center py-12">
              <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293A1 1 0 005 16h12M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
              </svg>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Giỏ hàng trống</h3>
              <p class="text-gray-600">Thêm sản phẩm vào giỏ hàng để bắt đầu mua sắm</p>
            </div>

            <!-- Cart Items List -->
            <div v-else class="space-y-6">
              <DataTable :value="listCart" dataKey="_id" rowHover scrollable :rows="1000" :loading="appStore.isLoading">
                <Column header="#">
                  <template #body="slotProps">
                    {{ slotProps.index + 1 }}
                  </template>
                </Column>

                <Column header="Mã sản phẩm" field="Material" class="min-w-200px"> </Column>
                <Column header="Tên sản phẩm" field="MaterialText"> </Column>
                <Column header="Số lượng" field="OrderQuantity" class="min-w-100px">
                  <template #body="slotProps">
                    {{ useFormatNumber(slotProps.data.OrderQuantity) }}
                  </template>
                </Column>
                <Column header="Đơn vị" field="OrderUnit" class="min-w-80px"> </Column>

                <!-- <Column header="Trọng lượng" field="NetWeight" class="min-w-130px">
                    <template #body="slotProps">
                      {{ useFormatNumber(slotProps.data.NetWeight) }}
                    </template>
                  </Column> -->
                <Column header="Giá NET" field="NetPrice" class="min-w-100px">
                  <template #body="slotProps">
                    {{ useFormatNumber(slotProps.data.NetPrice) }}
                  </template>
                </Column>
                <Column header="Trạng thái" field="StatusDesc" class="min-w-120px"> </Column>

                <Column header="Tổng (VNĐ)" :frozen="true" alignFrozen="right" field="TotalPrice" class="min-w-130px">
                  <template #body="slotProps">
                    {{ useFormatNumber(slotProps.data.TotalPrice) }}
                  </template>
                </Column>
                <template #empty>
                  <EmptyState />
                </template>
              </DataTable>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Summary & Checkout -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 sticky top-8">
          <div class="p-6">
            <h2 class="com-heading mb-6">Thông tin đặt hàng</h2>

            <!-- Order Summary -->
            <div class="space-y-3 mb-6">
              <div class="flex justify-between text-gray-600">
                <span> Giá ({{ listCart?.length }} sản phẩm)</span>
                <span>{{ useFormatNumber(calculateTotal) }} VNĐ</span>
              </div>
              <div class="flex justify-between text-gray-600">
                <span>Phí vận chuyển</span>
                <span>Miễn phí</span>
              </div>
              <div class="flex justify-between text-green-600">
                <span>Giảm giá</span>
                <span>0</span>
              </div>
              <div class="flex justify-between text-gray-600">
                <span>VAT</span>
                <span> {{ useFormatNumber(calculateTax) }} VNĐ</span>
              </div>
              <div class="border-t border-gray-200 pt-3">
                <div class="flex justify-between text-lg font-semibold text-gray-900">
                  <span>Tổng cộng</span>
                  <span>{{ useFormatNumber(calculateTotalTax) }} VNĐ</span>
                </div>
              </div>
            </div>

            <!-- Customer Information Form -->
            <form autocomplete="off" @submit.prevent="onSubmit" class="space-y-4">
              <h3 class="com-heading mb-4">Thông tin giao hàng</h3>

              <BaseInputText
                v-model="orderForm.sapCustomerId"
                :disabled="true"
                label="Mã khách hàng"
                name="sapCustomerId"
                placeholder="Nhập mã khách hàng" />
              <BaseInputText
                v-model="orderForm.shipToName"
                :disabled="true"
                label="Tên khách hàng"
                name="shipToName"
                placeholder="Nhập tên khách hàng" />
              <BaseInputText
                v-model="orderForm.shipToParty"
                :disabled="true"
                label="Địa chỉ"
                name="shipToParty"
                placeholder="Nhập địa chỉ" />
              <BaseInputText
                v-model="orderForm.phone"
                label="Số điện thoại"
                :disabled="true"
                name="phone"
                placeholder="Nhập số điện thoại" />
              <BaseInputTextArea
                v-model="orderForm.note"
                :disabled="true"
                label="Ghi chú"
                name="note"
                placeholder="Nhập ghi chú" />
              <!-- Place Order Button -->
              <!-- <button
                  type="submit"
                  :disabled="!orderForm.shipToParty || !orderForm.shipToName || isLoading"
                  class="w-full fr ai-c jc-c bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                  <span v-if="isLoading" class="flex items-center justify-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Đang xử lý...
                  </span>
                  <span v-else>Đặt hàng ngay</span>
                </button> -->

              <!-- <p class="text-xs text-gray-500 text-center mt-3">
                  Bằng việc đặt hàng, bạn đồng ý với
                  <a href="#" class="text-blue-600 hover:text-blue-700">Điều khoản dịch vụ</a>
                  và
                  <a href="#" class="text-blue-600 hover:text-blue-700">Chính sách bảo mật</a>
                </p> -->
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Modal -->
    <div
      v-if="showSuccessModal"
      class="fixed inset-0 bg-[#00000020] bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
        <div class="text-center">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2 text-center">Đặt hàng thành công!</h3>
          <p class="text-gray-600 mb-4">
            Đơn hàng #{{ result?.OrderNumber }} đã được tạo thành công. Chúng tôi sẽ liên hệ với bạn trong thời gian sớm
            nhất.
          </p>
          <div class="space-y-3">
            <button
              @click="continueShopping"
              class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-center">
              Tiếp tục mua sắm
            </button>
            <button
              @click="viewOrderDetails"
              class="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors text-center">
              Xem chi tiết đơn hàng
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
