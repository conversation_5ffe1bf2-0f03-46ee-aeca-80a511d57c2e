<template>
  <div class="min-h-screen bg-gray-50 w-full">
    <!-- Hero Section -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">How can we help you?</h1>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
          We're here to help and answer any question you might have. We look forward to hearing from you.
        </p>
      </div>
    </div>

    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
        <!-- Contact Information -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Get in Touch</h2>

            <!-- Contact Methods -->
            <div class="space-y-6">
              <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                  <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">Email</h3>
                  <p class="text-gray-600"><EMAIL></p>
                  <p class="text-sm text-gray-500 mt-1">We'll respond within 24 hours</p>
                </div>
              </div>

              <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                  <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                  </div>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">Phone</h3>
                  <p class="text-gray-600">+****************</p>
                  <p class="text-sm text-gray-500 mt-1">Mon-Fri 9AM-6PM EST</p>
                </div>
              </div>

              <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                  <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                  </div>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">Live Chat</h3>
                  <p class="text-gray-600">Available 24/7</p>
                  <button class="text-sm text-purple-600 hover:text-purple-700 mt-1 font-medium">Start chat →</button>
                </div>
              </div>

              <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                  <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                  </div>
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-900">Office</h3>
                  <p class="text-gray-600">123 Business St<br />Suite 100<br />City, State 12345</p>
                </div>
              </div>
            </div>

            <!-- FAQ Link -->
            <div class="mt-8 pt-8 border-t border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900 mb-3">Quick Help</h3>
              <div class="space-y-2">
                <a href="#" class="block text-blue-600 hover:text-blue-700 text-sm font-medium">
                  → Frequently Asked Questions
                </a>
                <a href="#" class="block text-blue-600 hover:text-blue-700 text-sm font-medium"> → Documentation </a>
                <a href="#" class="block text-blue-600 hover:text-blue-700 text-sm font-medium"> → Video Tutorials </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Form -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Send us a message</h2>

            <form @submit.prevent="submitForm" class="space-y-6">
              <!-- Name and Email Row -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="name" class="block text-sm font-medium text-gray-700 mb-2"> Full Name * </label>
                  <input
                    id="name"
                    v-model="form.name"
                    type="text"
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Enter your full name" />
                </div>
                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-2"> Email Address * </label>
                  <input
                    id="email"
                    v-model="form.email"
                    type="email"
                    required
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    placeholder="Enter your email" />
                </div>
              </div>

              <!-- Subject -->
              <div>
                <label for="subject" class="block text-sm font-medium text-gray-700 mb-2"> Subject * </label>
                <select
                  id="subject"
                  v-model="form.subject"
                  required
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                  <option value="">Select a subject</option>
                  <option value="general">General Inquiry</option>
                  <option value="technical">Technical Support</option>
                  <option value="billing">Billing Question</option>
                  <option value="feature">Feature Request</option>
                  <option value="bug">Bug Report</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <!-- Priority -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-3"> Priority Level </label>
                <div class="flex flex-wrap gap-4">
                  <label class="flex items-center">
                    <input
                      v-model="form.priority"
                      type="radio"
                      value="low"
                      class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500" />
                    <span class="ml-2 text-sm text-gray-700">Low</span>
                  </label>
                  <label class="flex items-center">
                    <input
                      v-model="form.priority"
                      type="radio"
                      value="medium"
                      class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500" />
                    <span class="ml-2 text-sm text-gray-700">Medium</span>
                  </label>
                  <label class="flex items-center">
                    <input
                      v-model="form.priority"
                      type="radio"
                      value="high"
                      class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500" />
                    <span class="ml-2 text-sm text-gray-700">High</span>
                  </label>
                  <label class="flex items-center">
                    <input
                      v-model="form.priority"
                      type="radio"
                      value="urgent"
                      class="w-4 h-4 text-red-600 border-gray-300 focus:ring-red-500" />
                    <span class="ml-2 text-sm text-gray-700">Urgent</span>
                  </label>
                </div>
              </div>

              <!-- Message -->
              <div>
                <label for="message" class="block text-sm font-medium text-gray-700 mb-2"> Message * </label>
                <textarea
                  id="message"
                  v-model="form.message"
                  rows="6"
                  required
                  maxlength="1000"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                  placeholder="Please describe your issue or question in detail..."></textarea>
                <p class="text-sm text-gray-500 mt-2">{{ form.message.length }}/1000 characters</p>
              </div>

              <!-- File Upload -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2"> Attachments (Optional) </label>
                <div
                  class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer">
                  <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                  <p class="text-sm text-gray-600 mb-1">
                    <span class="font-medium text-blue-600 hover:text-blue-700"> Click to upload </span>
                    or drag and drop
                  </p>
                  <p class="text-xs text-gray-500">PNG, JPG, PDF up to 10MB</p>
                </div>
              </div>

              <!-- Newsletter Checkbox -->
              <div class="flex items-start">
                <input
                  id="newsletter"
                  v-model="form.newsletter"
                  type="checkbox"
                  class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mt-1" />
                <label for="newsletter" class="ml-3 text-sm text-gray-700">
                  I'd like to receive updates and newsletters about new features and improvements.
                </label>
              </div>

              <!-- Submit Button -->
              <div class="flex items-center justify-between pt-4">
                <p class="text-sm text-gray-500">* Required fields</p>
                <button
                  type="submit"
                  :disabled="isSubmitting"
                  class="inline-flex items-center px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                  <svg v-if="!isSubmitting" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                  </svg>
                  <svg v-else class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {{ isSubmitting ? 'Sending...' : 'Send Message' }}
                </button>
              </div>
            </form>

            <!-- Success Message -->
            <div v-if="showSuccess" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                  <h3 class="text-sm font-medium text-green-800">Message sent successfully!</h3>
                  <p class="text-sm text-green-700 mt-1">
                    We've received your message and will get back to you within 24 hours.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- FAQ Section -->
    <div class="bg-white border-t border-gray-200">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
          <p class="text-lg text-gray-600">Quick answers to common questions</p>
        </div>

        <div class="space-y-4">
          <div v-for="(faq, index) in faqs" :key="index" class="border border-gray-200 rounded-lg">
            <button
              @click="toggleFaq(index)"
              class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
              <span class="font-medium text-gray-900">{{ faq.question }}</span>
              <svg
                class="w-5 h-5 text-gray-500 transition-transform"
                :class="{ 'rotate-180': faq.isOpen }"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            <div v-if="faq.isOpen" class="px-6 pb-4">
              <p class="text-gray-600">{{ faq.answer }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Form data
const form = reactive({
  name: '',
  email: '',
  subject: '',
  priority: 'medium',
  message: '',
  newsletter: false,
})

// Form state
const isSubmitting = ref(false)
const showSuccess = ref(false)

// FAQ data
const faqs = ref([
  {
    question: 'How long does it take to get a response?',
    answer:
      'We typically respond to all inquiries within 24 hours during business days. For urgent issues, please call our support line or use live chat.',
    isOpen: false,
  },
  {
    question: 'What information should I include in my support request?',
    answer:
      'Please include as much detail as possible about your issue, including steps to reproduce the problem, error messages, and your account information if applicable.',
    isOpen: false,
  },
  {
    question: 'Can I track the status of my support ticket?',
    answer:
      "Yes, once you submit a support request, you'll receive an email with a ticket number that you can use to track the status of your request.",
    isOpen: false,
  },
  {
    question: 'Do you offer phone support?',
    answer:
      'Yes, we offer phone support Monday through Friday from 9 AM to 6 PM EST. For after-hours support, please use our live chat or email support.',
    isOpen: false,
  },
  {
    question: 'Is there a knowledge base or documentation available?',
    answer:
      'Yes, we have comprehensive documentation and video tutorials available. You can access them through the links in the sidebar or visit our help center.',
    isOpen: false,
  },
])

// Methods
const submitForm = async () => {
  isSubmitting.value = true

  try {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Show success message
    showSuccess.value = true

    // Reset form
    Object.keys(form).forEach((key) => {
      if (typeof form[key] === 'boolean') {
        form[key] = false
      } else {
        form[key] = ''
      }
    })
    form.priority = 'medium'

    // Hide success message after 5 seconds
    setTimeout(() => {
      showSuccess.value = false
    }, 5000)
  } catch (error) {
    console.error('Error submitting form:', error)
  } finally {
    isSubmitting.value = false
  }
}

const toggleFaq = (index) => {
  faqs.value[index].isOpen = !faqs.value[index].isOpen
}
</script>
