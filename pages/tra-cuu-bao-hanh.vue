<script lang="ts" setup>
const product = ref('')
const isLoading = ref(false)
const info = ref<any>({})
const isEmpty = ref(false)
const getData = async () => {
  isLoading.value = true
  const body = [
    {
      ORDER: product.value,
    },
  ]
  const { data }: any = await useApi('production_order', {
    method: 'POST',
    body,
  })
  info.value = data.value ? data.value[0] : {}
  if (!info.value) {
    isEmpty.value = true
  }
  isLoading.value = false
}
</script>

<template>
  <div class="p-0 mx-a fc gap-4">
    <div class="page-heading text-center p-4">Tra cứu bảo hành</div>
    <div class="bg-[rgba(8,118,171,.25098)] fc gap-4 rounded">
      <div class="container mx-a fc gap-4">
        <div class="pt-10 text-center text-xl font-semibold c-primary">
          Vui lòng điền đầy đủ dãy số <strong><PERSON><PERSON> Sản <PERSON>ất</strong> trên sản phẩm vào ô tương ứng
        </div>
        <div class="grid grid-cols-8 px-10 p2-4 gap-4">
          <BaseInputText
            v-model="product"
            class="col-span-6"
            placeholder="Nhập mã sản xuất để tìm kiếm"
            :rules="{ required: false }" />
          <Button
            label="Tìm kiếm"
            severity="primary"
            class="col-span-2"
            :disabled="!product"
            :loading="isLoading"
            @click="getData" />
        </div>
        <div class="text-center c-primary mb-10">Tổng đài hỗ trợ bảo hành (08:00 - 17:00): 1800 6332</div>
        <div class="fc gap-4 mt-4 pb-10 w-500px mx-a" v-if="info?.ORDER">
          <div class="font-bold text-center text-2xl c-black">Kết quả</div>
          <div class="fr gap-4 ai-c w-full">
            <div class="text-base font-semibold w-200px">Mã sản xuất:</div>
            <div class="text-base font-bold c-primary">{{ useParseNumber(info?.ORDER) }}</div>
          </div>
          <div class="fr gap-4 ai-c w-full">
            <div class="text-base font-semibold w-200px">Mã sản phẩm:</div>
            <div class="text-base font-bold c-primary">{{ info?.MATERIAL }}</div>
          </div>
          <div class="fr gap-4 ai-c w-full">
            <div class="text-base font-semibold w-200px">Mặt cắt:</div>
            <div class="text-base font-bold c-primary">{{ info?.SECTION }}</div>
          </div>
          <div class="fr gap-4 ai-c w-full">
            <div class="text-base font-semibold w-200px">Thương hiệu:</div>
            <div class="text-base font-bold c-primary">{{ info?.PRODUCT_BRAND }}</div>
          </div>
          <div class="fr gap-4 ai-c w-full">
            <div class="text-base font-semibold w-200px">Tên sản phẩm:</div>
            <div class="text-base font-bold c-primary">{{ info?.DRAWING_DESCRIPTION }}</div>
          </div>
          <div class="fr gap-4 ai-c w-full">
            <div class="text-base font-semibold w-200px">Ngày sản xuất:</div>
            <div class="text-base font-bold c-primary">
              {{ useMoment(info?.NGAY_SAN_XUAT, 'DD-MM-YYYY') }}
            </div>
          </div>
          <div class="fr gap-4 ai-c w-full">
            <div class="text-base font-semibold w-200px">Thời hạn bảo hành *:</div>
            <div class="text-base font-bold c-primary">{{ info?.THOI_HAN_BAO_HANH }}</div>
          </div>
          <div class="fr gap-4 ai-c w-full">
            <div class="text-base font-bold c-primary">* Thời hạn bảo hành tính từ ngày sản xuất</div>
          </div>
        </div>
        <EmptyState v-if="isEmpty" />
      </div>
    </div>
    <div class="fc ai-c py-10 gap-8">
      <div class="text-center c-primary">
        Hướng dẫn tìm dãy số <strong> "Mã sản xuất"</strong> trên thanh nhôm An Lập Phát
      </div>
      <img src="~/assets/images/hd-baohanh.png" alt="" class="w-full max-w-600px object-cover" />
    </div>
    <div class="page-heading text-center p-4">Chi tiết về chính sách bảo hành</div>
    <div class="bg-[rgba(8,118,171,.25098)] fc gap-4 rounded">
      <div class="fr ai-c jc-sb max-w-500px w-full mx-a py-14">
        <nuxt-link to="/chinh-sach-bao-hanh" class="fc ai-c jc-c gap-4">
          <img src="~/assets/icons/csbh.svg" alt="" class="w-20 h-20 object-contain" />
          <div class="text-lg font-bold c-primary">Chính sách bảo hành</div>
        </nuxt-link>
        <nuxt-link to="#" class="fc ai-c jc-c gap-4">
          <img src="~/assets/icons/csbhang.svg" alt="" class="w-20 h-20 object-contain" />
          <div class="text-lg font-bold c-primary">Chính sách bán hàng</div>
        </nuxt-link>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped></style>
