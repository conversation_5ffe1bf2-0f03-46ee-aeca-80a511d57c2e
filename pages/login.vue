<script lang="ts" setup>
import { useForm } from 'vee-validate'
definePageMeta({
  layout: 'auth',
  auth: 'guest',
})

const route = useRoute()
const router = useRouter()
const toast = useToast()
const visible = ref(false)
const { handleSubmit, resetForm } = useForm()
const { $auth: auth } = useNuxtApp()

const form: any = ref({
  username: '',
  password: '',
})
const onSubmit = handleSubmit(async () => {
  try {
    auth
      .loginWith('user', {
        body: {
          username: form.value.username,
          password: form.value.password,
        },
      })
      .then((response: any) => {
        const { data, meta } = response._data

        if (meta?.code === 401) {
          toast.add({
            severity: 'error',
            summary: 'Thông báo',
            detail: meta?.message,
            life: 3000,
          })
        } else {
          toast.add({ severity: 'success', summary: 'Notifications', detail: 'Login Successfully', life: 3000 })
          router.push('/')
        }
      })
      .catch((error) => {
        toast.add({
          severity: 'error',
          summary: 'Thông báo',
          detail: `Thông tin đăng nhập không chính xác`,
          life: 3000,
        })
      })
  } catch (error) {
    if (error?.data?.statusCode === 401) {
      toast.add({
        severity: 'error',
        summary: 'Thông báo',
        detail: `Thông tin đăng nhập không chính xác`,
        life: 3000,
      })
    } else {
      toast.add({
        severity: 'error',
        summary: 'Thông báo',
        detail: `Sign in failed: \n ${error?.data?.message}`,
        life: 3000,
      })
    }
  }
})
const getGreeting = () => {
  const now = new Date()
  const hour = now.getHours()

  if (hour >= 5 && hour < 11) {
    return 'Chào buổi sáng,'
  } else if (hour >= 11 && hour < 13) {
    return 'Chào buổi trưa,'
  } else if (hour >= 13 && hour < 18) {
    return 'Chào buổi chiều,'
  } else {
    return 'Chào buổi tối,'
  }
}
</script>

<template>
  <div class="page py-4 px-9 fc">
    <div class="fr ai-c jc-c gap-4 mt-4">
      <img class="" src="~/assets/images/logo.svg" alt="" />
      <span class="text-lg font-bold c-black-90">Cổng thông tin khách hàng</span>
    </div>
    <div class="fc jc-c flex-1 mx-a w-full max-w-[500px]">
      <div class="fc w-full jc-fs py-20">
        <h1 class="mb-2 text-4xl font-bold c-primary">{{ getGreeting() }}</h1>
        <h1 class="mb-2 text-lg font-bold c-black-90">Vui lòng đăng nhập tài khoản của bạn.</h1>
      </div>

      <form autocomplete="off" @submit.prevent="onSubmit">
        <div class="mb-3 flex flex-col gap-1">
          <BaseInputText
            name="email"
            label="Tên đăng nhập / Email"
            placeholder="Tên đăng nhập / Email"
            :rules="{ required: true }"
            v-model="form.username" />
        </div>

        <div class="mb-3 flex flex-col gap-1">
          <BaseInputPassword
            name="password"
            label="Mật khẩu"
            placeholder="Mật khâu"
            :rules="{ required: true }"
            v-model="form.password" />
        </div>
        <div class="fr">
          <Button class="w-full flex" label="Đăng nhập" severity="primary" type="submit" />
        </div>
      </form>
    </div>
    <div class="text-center c-text">
      Bản quyền thuộc về công ty An Lập Phát <br />
      Phát triển bởi AIS-Tech
    </div>
  </div>
</template>
