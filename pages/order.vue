<script setup lang="ts">
import { useForm } from 'vee-validate'
import { useAppStore } from '~/stores/app'
const router = useRouter()
const appStore = useAppStore()
const showSuccessModal = ref(false)
const { $auth: auth }: any = useNuxtApp()
const { isCustomer } = useRole()
const sapCustomerId = ref(auth.user?.sapCustomerId || '')
const username = ref(auth.user?.username || '')
const listOrder = computed(() => appStore.listOrder)
const listAddress = ref<any>([])
const toast = useToast()
const { $dayjs: dayjs } = useNuxtApp()
const result = ref<any>({})
const listUnit = ref<any>(['BO', 'CAY', 'KG'])
const confirm = useConfirm()
const visibleDrawer = ref(false)
const productRelated = ref<any>([])
const productReplace = ref<any>([])
const orderForm = ref<any>({
  material: '',
  quantity: 1,
  sapCustomerId: sapCustomerId.value,
  shipToParty: '',
  shipToName: '',
  note: '',
  address: '',
  phone: '',
  unitOfMeasure: 'BO',
  deliveryDate: dayjs().add(1, 'day').format('YYYY-MM-DD'),
})

const isLoading = ref(false)
const calculateTotal = computed(() => {
  return listOrder.value.reduce((total: number, item: any) => total + useParseNumber(item?.netValue), 0)
})
const listCustomer = computed(() => appStore.listCustomer)
const calculateTax = computed(() => {
  return listOrder.value.reduce((total: number, item: any) => total + useParseNumber(item?.taxAmount), 0)
})
const calculateTotalTax = computed(() => {
  return listOrder.value.reduce(
    (total: number, item: any) => total + useParseNumber(item?.netValue) + useParseNumber(item?.taxAmount),
    0,
  )
})
const { handleSubmit, resetForm } = useForm()
const onSubmit = handleSubmit(async () => {
  try {
    isLoading.value = true
    const body = {
      OrderNumber: '',
      SAPOrderNumber: '',
      // Customer: this.form.customer || '',
      UserName: username.value,
      CustomerName: '',
      OrderDate: `/Date(${dayjs().valueOf()})/`,
      DeliveryDate: `/Date(${dayjs(orderForm.value.deliveryDate).valueOf()})/`,
      UnixORDate: `/Date(${dayjs().valueOf()})/`,
      UnixDLDate: `/Date(${dayjs().valueOf()})/`,
      ShipToParty: orderForm.value.sapCustomerId,
      ShipToName: orderForm.value.shipToName,
      ShipToAddress: '',
      // SalesOrg: this.form.SalesOrg,
      // DistributionChannel: this.form.DistributionChannel,
      // Division: this.form.Division,
      OrderType: 'ZFOC',
      Status: '20',
      StatusText: '',
      TotalPrice: '0.00',
      Currency: 'VND',
      TotalWeight: '0.00',
      WeightUnit: 'KG',
      Note: orderForm.value.note,
      RejectReason: '',
      FeUserId: auth.user.id.toString(),
      CreatedDate: `/Date(${dayjs().valueOf()})/`,
      CreatedBy: username.value,
      ChangedDate: `/Date(${dayjs().valueOf()})/`,
      ChangedBy: '',
      Address: orderForm.value.address,
      Phone: orderForm.value.phone,
      Items: listOrder.value.map((el: any, index: number) => {
        const data = {
          // SalesMaterial: el?.SalesMaterial?.toString() || '',
          Material: el?.Material || '',
          // OrderUnit: el?.Dvt?.toString() || '',
          // Status: el?.Status?.toString() || '',
          // MaterialDescription: el?.MaterialText?.toString() || '',
          OrderQuantity: useParseNumber(el?.quantity).toString() || '',
          // Points: el?.Points?.toString() || '',
          // VariantType: el?.VariantType?.toString() || '',
          // VariantValue: el?.VariantValue?.toString() || '',
          // PortalDescription: el?.PortalDescription?.toString() || '',
          // ColorCode: el?.ColorCode?.toString() || '',
          // Filename: el?.Filename?.toString() || '',
          // Hasattachment: el?.Hasattachment?.toString() || '',
        }
        return data
      }),
    }
    const { data }: any = await useApi(`so_create?sap_api_url=ZALP_SALES_ORDER_SRV/SOHeader`, {
      method: 'POST',
      body,
    })
    console.log(data.value, 'submit')
    if (data.value?.error) {
      toast.add({ severity: 'error', summary: 'Thông báo', detail: 'Đã xảy ra sự cố khi đặt hàng', life: 3000 })
    }
    if (data.value?.d) {
      // toast.add({
      //   severity: 'success',
      //   summary: 'Thông báo',
      //   detail: `Đơn hàng ${data.value?.d?.OrderNumber} đã đặt thành công`,
      //   life: 3000,
      // })
      appStore.listOrder = []
      result.value = data.value?.d
      showSuccessModal.value = true
    }
    isLoading.value = false
  } catch (error) {
  } finally {
    isLoading.value = false
  }
})

const getData = async () => {
  const { data }: any = await useApi(`sap_api_get`, {
    method: 'GET',
    params: {
      sap_api_url: `ZALP_SALES_ORDER_SRV/ShipToParty?$filter=Customer eq '${sapCustomerId.value}'&$format=json`,
    },
  })
  console.log(data.value, 'data')
  listAddress.value = data.value?.d?.results || []
  if (listAddress.value.length > 0) {
    orderForm.value.shipToParty = listAddress.value[0].Address
    orderForm.value.shipToName = listAddress.value[0].FullName
    orderForm.value.FullName = listAddress.value[0].FullName
    orderForm.value.sapCustomerId = listAddress.value[0].Customer
  }
}
const continueShopping = () => {
  router.push('/convert-point')
}
const viewOrderDetails = () => {
  showSuccessModal.value = false
  router.push(`/history/default/${result.value?.OrderNumber}`)
}
if (orderForm.value.sapCustomerId) {
  getData()
}
const updateQuantity = (item: any) => {
  //   const item = listOrder.value.find((el: any) => el.Materialnumber === material)
  orderForm.value.material = item.material
  orderForm.value.quantity = useParseNumber(item.OrderQuantity)
  calcPricing()
}
const addMaterial = () => {
  const index = listOrder.value.findIndex((el: any) => el.material === orderForm.value.material)
  console.log(index)
  if (index === -1) {
    calcPricing()
  } else {
    console.log('else')
    orderForm.value.quantity = useParseNumber(listOrder.value[index].OrderQuantity) + orderForm.value.quantity
    calcPricing()
  }
}
const resetOrderForm = () => {
  orderForm.value.material = ''
  orderForm.value.quantity = 1
}
const calcPricing = async (obj?: any) => {
  try {
    isLoading.value = true
    const body = {
      material: orderForm.value.material,
      quantity: orderForm.value.quantity,
      unitOfMeasure: orderForm.value.unitOfMeasure,
      sap_customer_id: sapCustomerId.value,
    }
    const url = encodeURIComponent(`material:calculate_pricing`)
    const { data }: any = await useApi(url, {
      method: 'POST',
      body: obj?.material ? obj : body,
    })
    if (data.value?.data?.material) {
      const item = {
        ...data.value?.data,
        OrderQuantity: useParseNumber(orderForm.value.quantity),
        Material: data.value?.data?.material,
        MaterialText: data.value?.data?.materialName,
        OrderUnit: data.value?.data?.weightUnit,
        NetPrice: useParseNumber(data.value?.data?.netPrice),
        TotalPrice: useParseNumber(data.value?.data?.netValue),
      }
      const index = listOrder.value.findIndex((el: any) => el.material === item.material)
      if (index !== -1) {
        appStore.listOrder[index] = item
      } else {
        appStore.listOrder.push(item)
      }
      resetOrderForm()
    } else {
      toast.add({ severity: 'error', summary: 'Thông báo', detail: 'Không tìm thấy sản phẩm', life: 3000 })
      resetOrderForm()
    }
    console.log(data.value, 'submit')
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Thông báo', detail: 'Đã xảy ra sự cố khi thêm sản phẩm', life: 3000 })
  } finally {
    isLoading.value = false
  }
}
const removeMaterial = (material: any) => {
  confirm.require({
    message: 'Bạn có chắc chắn muốn xóa sản phẩm này không?',
    header: 'Xác nhận',
    icon: 'pi pi-exclamation-triangle',
    rejectLabel: 'Hủy',
    acceptLabel: 'Xóa',
    acceptClass: 'p-button-danger',
    rejectClass: 'p-button-help',
    accept: () => {
      const index = listOrder.value.findIndex((el: any) => el.material === material.material)
      if (index !== -1) {
        listOrder.value.splice(index, 1)
      }
    },
  })
}
const findMaterialRelated = async (record: any) => {
  try {
    appStore.isLoading = true
    const body = {
      material: record.material,
      quantity: record.OrderQuantity,
      unitOfMeasure: record.OrderUnit,
      sap_customer_id: sapCustomerId.value,
    }
    const url = encodeURIComponent('material:related')
    const { data }: any = await useApi(url, {
      method: 'POST',
      body,
    })
    console.log(data.value)
    const newData = data.value?.data?.map((el: any) => {
      el.Materialnumber = el.material
      el.PortalDescription = el.material
      return el
    })
    productRelated.value = newData.filter((el: any) => el.relationType == 'RELT') || []
    productReplace.value = newData.filter((el: any) => el.relationType == 'STIT') || []
    visibleDrawer.value = true
    appStore.isLoading = false
  } catch (error) {
  } finally {
    appStore.isLoading = false
  }
}
const addMaterialRelated = (item: any) => {
  orderForm.value.material = item.Materialnumber
  orderForm.value.quantity = 1
  addMaterial()
}

watch(
  () => listCustomer.value,
  (val) => {
    console.log(sapCustomerId.value, 'sapCustomerId.value')

    if (val.length > 0 && !sapCustomerId.value) {
      sapCustomerId.value = val[0]?.CustomerID || ''
    }
  },
  {
    immediate: true,
  },
)
watch(
  () => orderForm.value.sapCustomerId,
  (val) => {
    const item: any = listAddress.value.find((item: any) => item.Customer === val)
    if (item) {
      orderForm.value.shipToParty = item.Address
      orderForm.value.shipToName = item.FullName
      orderForm.value.FullName = item.FullName
    }
  },
  {
    immediate: true,
  },
)
watch(
  () => orderForm.value.FullName,
  (val) => {
    const item: any = listAddress.value.find((item: any) => item.FullName === val)
    if (item) {
      orderForm.value.shipToParty = item.Address
      orderForm.value.shipToName = item.FullName
      // orderForm.value.FullName = item.FullName
    }
  },
  {
    immediate: true,
  },
)
watch(
  () => sapCustomerId.value,
  (val) => {
    getData()
    orderForm.value.shipToParty = ''
    orderForm.value.shipToName = ''
    if (listOrder.value.length > 0) {
      // listOrder.value = []
      listOrder.value.forEach((item: any) => {
        const obj = {
          material: item.material,
          quantity: item.OrderQuantity,
          unitOfMeasure: item.OrderUnit,
          sap_customer_id: sapCustomerId.value,
        }
        calcPricing(obj)
      })
    }
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <div class="container mx-auto min-h-80vh">
    <!-- Header -->
    <div class="mb-4">
      <h1 class="page-heading">Tạo đơn hàng</h1>
      <p class="text-gray-600 mt-2">Tìm kiếm sản phẩm để thêm vào giỏ hàng</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Cart Items -->
      <div class="lg:col-span-2 fc gap-4">
        <div class="grid p-4 rounded bg-white gap-4" :class="isCustomer ? 'grid-cols-4' : 'grid-cols-4'">
          <SearchMaterialSingle v-model:material="orderForm.material" />
          <BaseInputNumber v-model="orderForm.quantity" label="Số lượng" name="quantity" />
          <BaseInputSelect
            v-model="orderForm.unitOfMeasure"
            :options="listUnit"
            label="Đơn vị"
            option-label=""
            option-value=""
            name="unitOfMeasure" />
          <div class="fc">
            <div class="flex-1"></div>
            <Button
              label="Thêm sản phẩm"
              class="h-44.5px"
              @click="addMaterial"
              :loading="isLoading"
              :disabled="isLoading || !orderForm.material" />
          </div>
        </div>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6">
            <h2 class="com-heading mb-6">Sản phẩm đã chọn</h2>

            <!-- Empty Cart -->
            <div v-if="listOrder.length === 0" class="text-center py-12">
              <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293A1 1 0 005 16h12M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
              </svg>
              <h3 class="text-lg font-medium text-gray-900 mb-2">Giỏ hàng trống</h3>
              <p class="text-gray-600">Thêm sản phẩm vào giỏ hàng để bắt đầu mua sắm</p>
            </div>

            <!-- Cart Items List -->
            <div v-else class="space-y-6">
              <DataTable
                :value="listOrder"
                dataKey="_id"
                rowHover
                scrollable
                :rows="1000"
                :loading="appStore.isLoading">
                <Column header="#">
                  <template #body="slotProps">
                    {{ slotProps.index + 1 }}
                  </template>
                </Column>

                <Column header="Mã sản phẩm" field="Material" class="min-w-200px"> </Column>
                <Column header="Tên sản phẩm" field="MaterialText"> </Column>
                <Column header="Số lượng" field="OrderQuantity" class="min-w-100px">
                  <template #body="slotProps">
                    <!-- {{ useFormatNumber(slotProps.data.OrderQuantity) }} -->
                    <input
                      v-model="slotProps.data.OrderQuantity"
                      class="w-50px"
                      type="number"
                      min="1"
                      @change="updateQuantity(slotProps.data)" />
                  </template>
                </Column>
                <Column header="Đơn vị" field="OrderUnit" class="min-w-80px"> </Column>

                <!-- <Column header="Trọng lượng" field="NetWeight" class="min-w-130px">
                    <template #body="slotProps">
                      {{ useFormatNumber(slotProps.data.NetWeight) }}
                    </template>
                  </Column> -->
                <Column header="Giá NET" field="NetPrice" class="min-w-100px">
                  <template #body="slotProps">
                    {{ useFormatNumber(slotProps.data.NetPrice) }}
                  </template>
                </Column>
                <!-- <Column header="Trạng thái" field="StatusDesc" class="min-w-120px"> </Column> -->

                <Column header="Tổng (VNĐ)" :frozen="true" alignFrozen="right" field="TotalPrice" class="min-w-130px">
                  <template #body="slotProps">
                    {{ useFormatNumber(slotProps.data.TotalPrice) }}
                  </template>
                </Column>
                <Column header="Hành động" :frozen="true" alignFrozen="right" field="action" class="min-w-120px">
                  <template #body="slotProps">
                    <div class="fr ai-c gap-4">
                      <img
                        v-tooltip.top="'Xóa'"
                        src="~/assets/icons/i-trash-red.svg"
                        alt=""
                        class="cursor-pointer"
                        @click="removeMaterial(slotProps.data)" />
                      <img
                        v-tooltip.top="'Gợi ý sản phẩm'"
                        src="~/assets/icons/i-suggest.svg"
                        alt=""
                        class="cursor-pointer"
                        @click="findMaterialRelated(slotProps.data)" />
                    </div>
                  </template>
                </Column>
                <template #empty>
                  <EmptyState />
                </template>
              </DataTable>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Summary & Checkout -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 sticky top-8">
          <div class="p-6">
            <h2 class="com-heading mb-6">Thông tin đặt hàng</h2>

            <!-- Order Summary -->
            <div class="space-y-3 mb-6">
              <div class="flex justify-between text-gray-600">
                <span>Tạm tính ({{ listOrder?.length }} sản phẩm)</span>
                <span>{{ useFormatNumber(calculateTotal) }} VNĐ</span>
              </div>
              <div class="flex justify-between text-gray-600">
                <span>Phí vận chuyển</span>
                <span>Miễn phí</span>
              </div>
              <div class="flex justify-between text-green-600">
                <span>Giảm giá</span>
                <span>0</span>
              </div>
              <div class="flex justify-between text-gray-600">
                <span>VAT</span>
                <span> {{ useFormatNumber(calculateTax) }} VNĐ</span>
              </div>
              <div class="border-t border-gray-200 pt-3">
                <div class="flex justify-between text-lg font-semibold text-gray-900">
                  <span>Tổng cộng</span>
                  <span>{{ useFormatNumber(calculateTotalTax) }} VNĐ</span>
                </div>
              </div>
            </div>

            <!-- Customer Information Form -->
            <form autocomplete="off" @submit.prevent="onSubmit" class="space-y-4">
              <h3 class="com-heading mb-4">Thông tin giao hàng</h3>

              <!-- Full Name -->
              <SelectCustomer v-model:customer="sapCustomerId" v-if="!isCustomer" />
              <!-- <BaseInputSelect
                v-if="isCustomer"
                :options="listAddress"
                optionLabel="FullName"
                optionValue="Customer"
                v-model="orderForm.sapCustomerId"
                label="Khách hàng"
                name="sapCustomerId"
                placeholder="Nhập khách hàng" /> -->
              <BaseInputSelect
                :options="listAddress"
                optionLabel="FullName"
                optionValue="FullName"
                v-model="orderForm.FullName"
                label="Nơi giao"
                name="sapCustomerId"
                placeholder="Nhập khách hàng" />
              <!-- <BaseInputText
                v-model="orderForm.sapCustomerId"
                :disabled="true"
                label="Mã khách hàng"
                name="sapCustomerId"
                placeholder="Nhập mã khách hàng" /> -->
              <BaseInputText
                v-model="orderForm.shipToParty"
                :disabled="true"
                label="Địa chỉ"
                name="shipToParty"
                placeholder="Nhập địa chỉ" />
              <BaseInputCalendar
                v-model="orderForm.deliveryDate"
                label="Ngày giao hàng"
                name="deliveryDate"
                :min-date="new Date()" />
              <BaseInputText
                v-model="orderForm.phone"
                label="Số điện thoại"
                name="phone"
                placeholder="Nhập số điện thoại" />
              <BaseInputTextArea v-model="orderForm.note" label="Ghi chú" name="note" placeholder="Nhập ghi chú" />
              <!-- Place Order Button -->
              <button
                type="submit"
                :disabled="!orderForm.shipToParty || !orderForm.shipToName || isLoading || listOrder.length === 0"
                class="w-full fr ai-c jc-c bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                <span v-if="isLoading" class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Đang xử lý...
                </span>
                <span v-else>Đặt hàng ngay</span>
              </button>

              <p class="text-xs text-gray-500 text-center mt-3">
                Bằng việc đặt hàng, bạn đồng ý với
                <a href="#" class="text-blue-600 hover:text-blue-700">Điều khoản dịch vụ</a>
                và
                <a href="#" class="text-blue-600 hover:text-blue-700">Chính sách bảo mật</a>
              </p>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Modal -->
    <div
      v-if="showSuccessModal"
      class="fixed inset-0 bg-[#00000020] bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
        <div class="text-center">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-gray-900 mb-2 text-center">Đặt hàng thành công!</h3>
          <p class="text-gray-600 mb-4">
            Đơn hàng #{{ result?.OrderNumber }} đã được tạo thành công. Chúng tôi sẽ liên hệ với bạn trong thời gian sớm
            nhất.
          </p>
          <div class="space-y-3">
            <button
              @click="continueShopping"
              class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-center">
              Tiếp tục mua sắm
            </button>
            <button
              @click="viewOrderDetails"
              class="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors text-center">
              Xem chi tiết đơn hàng
            </button>
          </div>
        </div>
      </div>
    </div>
    <Sidebar v-model:visible="visibleDrawer" header="Sidebar" position="right" class="!w-500px">
      <div class="fc">
        <div class="page-heading">Sản phẩm liên quan</div>
        <div class="fc gap-2 mt-4 max-h-38vh overflow-auto hide-scroll-bar">
          <div
            class="fr jc-sb ai-c gap-4 border-b-1 border-b-solid border-b-gray-200 py-2"
            v-for="item in productRelated"
            v-if="productRelated.length > 0">
            <div class="flex-1 fc gap-1">
              <div class="text-base font-bold c-primary">{{ item.PortalDescription }}</div>
              <div class="text-sm font-normal c-black-80">{{ item.Materialnumber }}</div>
            </div>
            <div
              class="w-32px h-32px bg-primary hover:opacity-80 transition-all fc ai-c jc-c rounded cursor-pointer"
              @click="addMaterialRelated(item)">
              <img src="@/assets/icons/i-add-white.svg" class="w-20px" alt="" />
            </div>
          </div>
          <EmptyState v-else />
        </div>
        <div class="page-heading">Sản phẩm thay thế</div>
        <div class="fc gap-2 mt-4 max-h-38vh overflow-auto hide-scroll-bar">
          <div
            class="fr jc-sb ai-c gap-4 border-b-1 border-b-solid border-b-gray-200 py-2"
            v-for="item in productReplace"
            v-if="productReplace.length > 0">
            <div class="flex-1 fc gap-1">
              <div class="text-base font-bold c-primary">{{ item.PortalDescription }}</div>
              <div class="text-sm font-normal c-black-80">{{ item.Materialnumber }}</div>
            </div>
            <div
              class="w-32px h-32px bg-primary hover:opacity-80 transition-all fc ai-c jc-c rounded cursor-pointer"
              @click="addMaterialRelated(item)">
              <img src="@/assets/icons/i-add-white.svg" class="w-20px" alt="" />
            </div>
          </div>
          <EmptyState v-else />
        </div>
      </div>
    </Sidebar>
  </div>
</template>
