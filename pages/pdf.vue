<template>
  <div class="flipbook-container">
    <div class="header">
      <h1 class="title">PDF Flipbook Viewer</h1>
      <div class="controls">
        <input type="file" @change="handleFileUpload" accept=".pdf" class="file-input" id="pdf-upload" />
        <label for="pdf-upload" class="upload-btn">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          Upload PDF
        </label>
        <div class="page-info" v-if="totalPages > 0">
          Trang {{ currentLeftPage }}<span v-if="currentRightPage">-{{ currentRightPage }}</span> / {{ totalPages }}
        </div>
      </div>
    </div>

    <div class="flipbook-wrapper" v-if="pdfLoaded">
      <div class="book-container">
        <div class="book" ref="book">
          <!-- Base Pages (always visible) -->
          <div class="page-spread">
            <!-- Left Page -->
            <div class="page left-page">
              <div class="page-content">
                <img v-if="leftPageImage" :src="leftPageImage" alt="Left page" class="page-image" />
                <div v-else class="empty-page">
                  <span>Trang trống</span>
                </div>
              </div>
              <div class="page-number">{{ currentLeftPage }}</div>
            </div>

            <!-- Right Page -->
            <div class="page right-page">
              <div class="page-content">
                <img v-if="rightPageImage" :src="rightPageImage" alt="Right page" class="page-image" />
                <div v-else class="empty-page">
                  <span>Trang trống</span>
                </div>
              </div>
              <div class="page-number" v-if="currentRightPage">{{ currentRightPage }}</div>
            </div>
          </div>

          <!-- Flipping Page (animation layer) -->
          <div
            v-if="isFlipping"
            class="flipping-page"
            :class="flipDirection"
            :style="{ transform: `rotateY(${flipAngle}deg)` }">
            <div class="flip-front">
              <div class="page-content">
                <img v-if="flipFrontImage" :src="flipFrontImage" alt="Flip front" class="page-image" />
              </div>
            </div>
            <div class="flip-back">
              <div class="page-content">
                <img v-if="flipBackImage" :src="flipBackImage" alt="Flip back" class="page-image" />
              </div>
            </div>
          </div>

          <!-- Book Spine -->
          <div class="book-spine"></div>
        </div>
      </div>

      <!-- Navigation Controls -->
      <div class="navigation-container">
        <div class="navigation">
          <button @click="previousPage" :disabled="currentLeftPage <= 1 || isFlipping" class="nav-btn prev-btn">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            Trang trước
          </button>

          <div class="page-slider">
            <input
              type="range"
              :min="1"
              :max="Math.ceil(totalPages / 2)"
              v-model="currentSpread"
              @input="goToSpread"
              class="slider"
              :disabled="isFlipping" />
            <div class="slider-labels">
              <span>1</span>
              <span>{{ Math.ceil(totalPages / 2) }}</span>
            </div>
          </div>

          <button @click="nextPage" :disabled="!hasNextPage || isFlipping" class="nav-btn next-btn">
            Trang sau
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <div v-else class="upload-area">
      <div class="upload-placeholder">
        <svg class="w-16 h-16 text-gray-400 mb-4 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p class="text-lg text-gray-600 mb-2">Chưa có PDF</p>
        <p class="text-sm text-gray-500">Tải lên file PDF để bắt đầu đọc</p>
      </div>
    </div>

    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p class="mt-4 text-gray-700">Đang xử lý PDF...</p>
        <div class="progress-bar" v-if="loadingProgress > 0">
          <div class="progress-fill" :style="{ width: loadingProgress + '%' }"></div>
        </div>
        <p class="text-sm text-gray-500 mt-2">{{ loadingProgress }}%</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'

// Reactive data
const book = ref(null)
const pageImages = ref([])
const totalPages = ref(0)
const currentSpread = ref(1)
const pdfLoaded = ref(false)
const loading = ref(false)
const loadingProgress = ref(0)
const isFlipping = ref(false)
const flipDirection = ref('right')
const flipAngle = ref(0)

// Page images
const leftPageImage = ref('')
const rightPageImage = ref('')
const flipFrontImage = ref('')
const flipBackImage = ref('')

// Computed properties
const currentLeftPage = computed(() => {
  return (currentSpread.value - 1) * 2 + 1
})

const currentRightPage = computed(() => {
  const rightPage = (currentSpread.value - 1) * 2 + 2
  return rightPage <= totalPages.value ? rightPage : null
})

const hasNextPage = computed(() => {
  return currentRightPage.value && currentRightPage.value < totalPages.value
})

// Watch for page changes - chỉ update khi không đang flip
watch(
  [currentLeftPage, currentRightPage],
  async () => {
    if (pdfLoaded.value && !isFlipping.value) {
      await updatePageImages()
    }
  },
  { immediate: true },
)

// Handle file upload
const handleFileUpload = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  loading.value = true
  loadingProgress.value = 0

  try {
    await loadPDFFromFile(file)
  } catch (error) {
    console.error('Error loading PDF:', error)
    alert('Lỗi khi tải file PDF: ' + error.message)
  } finally {
    loading.value = false
    loadingProgress.value = 0
  }
}

// Load PDF from file
const loadPDFFromFile = async (file) => {
  try {
    // Load PDF.js dynamically if not available
    if (!window.pdfjsLib) {
      await loadPDFJS()
    }

    const arrayBuffer = await file.arrayBuffer()
    const pdf = await window.pdfjsLib.getDocument({ data: arrayBuffer }).promise

    totalPages.value = pdf.numPages
    pageImages.value = []

    // Convert each page to image with progress
    for (let pageNum = 1; pageNum <= totalPages.value; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum)
        const scale = 1
        const viewport = page.getViewport({ scale })

        // Create canvas
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')
        canvas.height = viewport.height
        canvas.width = viewport.width

        // Render page to canvas
        await page.render({
          canvasContext: context,
          viewport: viewport,
        }).promise

        // Convert canvas to image URL
        const imageUrl = canvas.toDataURL('image/jpeg', 0.8)
        pageImages.value.push(imageUrl)

        // Update progress
        loadingProgress.value = Math.round((pageNum / totalPages.value) * 100)
      } catch (error) {
        console.error(`Error converting page ${pageNum}:`, error)
        pageImages.value.push(createPlaceholderImage(pageNum))
      }
    }

    pdfLoaded.value = true
    currentSpread.value = 1
    await nextTick()
    await updatePageImages()
  } catch (error) {
    console.error('Error processing PDF:', error)
    throw error
  }
}

// Load PDF.js dynamically
const loadPDFJS = () => {
  return new Promise((resolve, reject) => {
    if (window.pdfjsLib) {
      resolve()
      return
    }

    const script = document.createElement('script')
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'
    script.onload = () => {
      window.pdfjsLib.GlobalWorkerOptions.workerSrc =
        'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
      resolve()
    }
    script.onerror = reject
    document.head.appendChild(script)
  })
}

// Create placeholder image for failed pages
const createPlaceholderImage = (pageNum) => {
  const canvas = document.createElement('canvas')
  canvas.width = 400
  canvas.height = 600
  const ctx = canvas.getContext('2d')

  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, canvas.width, canvas.height)

  ctx.strokeStyle = '#cccccc'
  ctx.strokeRect(10, 10, canvas.width - 20, canvas.height - 20)

  ctx.fillStyle = '#666666'
  ctx.font = '24px Arial'
  ctx.textAlign = 'center'
  ctx.fillText(`Trang ${pageNum}`, canvas.width / 2, canvas.height / 2 - 20)
  ctx.font = '16px Arial'
  ctx.fillText('Không thể tải', canvas.width / 2, canvas.height / 2 + 10)

  return canvas.toDataURL('image/png')
}

// Update page images for current spread
const updatePageImages = async () => {
  if (!pageImages.value.length) return

  leftPageImage.value = pageImages.value[currentLeftPage.value - 1] || ''
  rightPageImage.value = currentRightPage.value ? pageImages.value[currentRightPage.value - 1] || '' : ''
}

// Animation function - đơn giản và mượt
const animateFlip = (startAngle, endAngle, duration = 600) => {
  const startTime = performance.now()

  const animate = (currentTime) => {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)

    // Simple easing - không quá phức tạp
    const easeOut = 1 - Math.pow(1 - progress, 3)

    flipAngle.value = startAngle + (endAngle - startAngle) * easeOut

    if (progress < 1) {
      requestAnimationFrame(animate)
    } else {
      // Kết thúc animation - đơn giản
      isFlipping.value = false
      flipAngle.value = 0
      flipFrontImage.value = ''
      flipBackImage.value = ''
      updatePageImages()
    }
  }

  requestAnimationFrame(animate)
}

// Navigation functions
const nextPage = async () => {
  if (hasNextPage.value && !isFlipping.value) {
    await flipPage('right')
  }
}

const previousPage = async () => {
  if (currentLeftPage.value > 1 && !isFlipping.value) {
    await flipPage('left')
  }
}

const flipPage = async (direction) => {
  if (isFlipping.value) return

  isFlipping.value = true
  flipDirection.value = direction

  if (direction === 'right') {
    flipFrontImage.value = rightPageImage.value
    if (currentRightPage.value + 1 <= totalPages.value) {
      flipBackImage.value = pageImages.value[currentRightPage.value] || ''
    }

    currentSpread.value++
    animateFlip(0, -180)
  } else {
    flipFrontImage.value = leftPageImage.value
    if (currentLeftPage.value - 2 > 0) {
      flipBackImage.value = pageImages.value[currentLeftPage.value - 3] || ''
    }

    currentSpread.value--
    animateFlip(0, 180)
  }
}

const goToSpread = () => {
  if (!isFlipping.value) {
    updatePageImages()
  }
}
</script>

<style scoped>
.flipbook-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #fef7cd 0%, #fed7aa 100%);
  padding: 1rem;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

@media (min-width: 640px) {
  .header {
    flex-direction: row;
    justify-content: space-between;
  }
}

.title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #374151;
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .title {
    margin-bottom: 0;
  }
}

.controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.file-input {
  display: none;
}

.upload-btn {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  background: #2563eb;
  color: white;
}

.upload-btn:hover {
  background: #1d4ed8;
}

.page-info {
  font-size: 0.875rem;
  color: #4b5563;
  font-weight: 500;
}

.flipbook-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.book-container {
  position: relative;
}

.book {
  position: relative;
  width: 800px;
  height: 600px;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  perspective: 1200px;
  transform-style: preserve-3d;
}

.page-spread {
  display: flex;
  width: 100%;
  height: 100%;
}

.page {
  position: relative;
  width: 100%;
  height: 100%;
  background: white;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
}

.left-page {
  border-right: 1px solid #d1d5db;
}

.right-page {
  border-left: 1px solid #d1d5db;
}

.page-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  overflow: hidden;
}

.page-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.empty-page {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
}

.page-number {
  position: absolute;
  bottom: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.8);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  z-index: 10;
}

.flipping-page {
  position: absolute;
  top: 0;
  width: 50%;
  height: 100%;
  background: white;
  transform-style: preserve-3d;
  z-index: 20;
}

.flipping-page.right {
  right: 0;
  transform-origin: left center;
}

.flipping-page.left {
  left: 0;
  transform-origin: right center;
}

.flip-front,
.flip-back {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border: 1px solid #e5e7eb;
}

.flip-back {
  transform: rotateY(180deg);
}

.book-spine {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, #92400e, #78350f);
  z-index: 5;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

.navigation-container {
  width: 100%;
  max-width: 800px;
}

.navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  position: relative;
  z-index: 30;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #fef3c7;
  border-radius: 0.5rem;
  transition: all 0.2s;
  font-weight: 500;
  font-size: 0.875rem;
  min-width: 120px;
  justify-content: center;
  border: none;
  cursor: pointer;
}

.nav-btn:hover:not(:disabled) {
  background: #fde68a;
}

.nav-btn:disabled {
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

.page-slider {
  flex: 1;
  margin: 0 1rem;
}

.slider {
  width: 100%;
  height: 0.5rem;
  background: #e5e7eb;
  border-radius: 0.5rem;
  appearance: none;
  cursor: pointer;
}

.slider::-webkit-slider-thumb {
  appearance: none;
  width: 1rem;
  height: 1rem;
  background: #d97706;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background: #d97706;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.upload-area {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.upload-placeholder {
  text-align: center;
}

.loading-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.loading-spinner {
  background: white;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  min-width: 20rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 4px solid #d97706;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.progress-bar {
  width: 100%;
  background: #e5e7eb;
  border-radius: 9999px;
  height: 0.5rem;
  margin-top: 1rem;
}

.progress-fill {
  background: #d97706;
  height: 0.5rem;
  border-radius: 9999px;
  transition: width 0.3s ease;
}
</style>
