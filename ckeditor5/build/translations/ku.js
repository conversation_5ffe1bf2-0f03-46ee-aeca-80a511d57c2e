!function(e){const t=e.ku=e.ku||{};t.dictionary=Object.assign(t.dictionary||{},{"%0 of %1":"%0 لە %1",Accept:"","Align center":"بەهێڵکردنی ناورەڕاست","Align left":"بەهێڵکردنی چەپ","Align right":"بەهێڵکردنی ڕاست",Aquamarine:"شینی دەریایی",Black:"ڕەش","Block quote":"وتەی وەرگیراو",Blue:"شین",Bold:"قەڵەو","Break text":"","Bulleted List":"لیستەی خاڵەیی",Cancel:"هەڵوەشاندنەوە","Caption for image: %0":"","Caption for the image":"","Centered image":"ناوەڕاستکراوی وێنە","Change image text alternative":"گۆڕینی جێگروەی تێکیسی وێنە","Choose heading":"سەرنووسە هەڵبژێرە","Color picker":"",Column:"ستوون","Delete column":"سڕینەوەی ستوون","Delete row":"سڕینەوەی ڕیز","Dim grey":"ڕەساسی تاریک","Document colors":"ڕەنگەکانی دۆکومێنت",Downloadable:"Downloadable","Dropdown toolbar":"تووڵامرازی لیستەیی","Edit block":"دەستکاری بلۆک","Edit link":"دەستکاری بەستەر","Editor block content toolbar":"","Editor contextual toolbar":"","Editor editing area: %0":"","Editor toolbar":"تووڵامرازی دەسکاریکەر","Enter image caption":"سەردێڕی وێنە دابنێ","Font Background Color":"ڕەنگی پاشبنەمای فۆنت","Font Color":"ڕەنگی فۆنت","Full size image":"پڕ بەقەبارەی وێنە",Green:"سەوز",Grey:"ڕەساسی","Header column":"ستوونی دەسپێک","Header row":"ڕیزی دەسپێک",Heading:"سەرنووسە","Heading 1":"سەرنووسەی 1","Heading 2":"سەرنووسەی 2","Heading 3":"سەرنووسەی 3","Heading 4":"سەرنووسەی 4","Heading 5":"سەرنووسەی 5","Heading 6":"سەرنووسەی 6",HEX:"","Image resize list":"","Image toolbar":"تووڵامرازی وێنە","image widget":"وێدجیتی وێنە","In line":"",Insert:"","Insert column left":"دانانی ستوون لە چەپ","Insert column right":"دانانی ستوون لە ڕاست","Insert image":"وێنە دابنێ","Insert image via URL":"","Insert media":"مێدیا دابنێ","Insert paragraph after block":"","Insert paragraph before block":"","Insert row above":"دانانی ڕیز لە سەرەوە","Insert row below":"دانانی ڕیز لە ژێرەوە","Insert table":"خشتە دابنێ",Italic:"لار",Justify:"هاوستوونی","Left aligned image":"ڕیزکردنی وێنە بۆ لای چەپ","Light blue":"شینی ڕووناک","Light green":"سەوزی ڕووناک","Light grey":"ڕەساسی ڕووناک",Link:"بەستەر","Link URL":"ناونیشانی بەستەر","Media URL":"بەستەری مێدیا","media widget":"ویدجێتتی مێدیا","Merge cell down":"تێکەڵکردنی خانەکان بەرەو ژێرەوە","Merge cell left":"تێکەڵکردنی خانەکان بەرەو چەپ","Merge cell right":"تێکەڵکردنی خانەکان بەرەو ڕاست","Merge cell up":"تێکەڵکردنی خانەکان بەرەو سەر","Merge cells":"تێکەڵکردنی خانەکان",Next:"دواتر","Numbered List":"لیستەی ژمارەیی","Open in a new tab":"کردنەوەی لە پەنجەرەیەکی نوێ","Open link in new tab":"کردنەوەی بەستەرەکە لە پەڕەیەکی نوێ","Open media in new tab":"",Orange:"پرتەقاڵی",Original:"",Paragraph:"پەراگراف","Paste the media URL in the input.":"بەستەری مێدیاکە لە خانەکە بلکێنە.","Press Enter to type after or press Shift + Enter to type before the widget":"",Previous:"پێشتر",Purple:"مۆر",Red:"سور",Redo:"هەلگەڕاندنەوە","Remove color":"لابردنی ڕەنگ","Resize image":"","Resize image to %0":"","Resize image to the original size":"","Rich Text Editor":"سەرنوسەری دەقی بەپیت","Right aligned image":"ڕیزکردنی وێنە بۆ لای ڕاست",Row:"ڕیز",Save:"پاشکەوتکردن","Select column":"","Select row":"","Show more items":"بڕگەی زیاتر نیشانبدە","Side image":"لای وێنە","Split cell horizontally":"بەشکردنی خانەکان بە ئاسۆیی","Split cell vertically":"بەشکردنی خانەکان بە ئەستوونی",Strikethrough:"هێڵ بەسەرداهاتوو","Table toolbar":"تووڵامرازی خشتە","Text alignment":"ڕیززکردنی تێکست","Text alignment toolbar":"تووڵامرازی ڕیززکردنی تێکست","Text alternative":"جێگرەوەی تێکست","The URL must not be empty.":"پێویستە بەستەر بەتاڵ نەبێت.","This link has no URL":"ئەم بەستەرە ناونیشانی نیە","This media URL is not supported.":"ئەم بەستەری مێدیایە پاڵپشتی ناکرێت.","Tip: Paste the URL into the content to embed faster.":"Tip: Paste the URL into the content to embed faster.","Toggle caption off":"","Toggle caption on":"",Turquoise:"شینی ئاسمانی",Underline:"ژێرهێڵ",Undo:"وەک خۆی لێ بکەوە",Unlink:"لابردنی بەستەر",Update:"","Update image URL":"","Upload failed":"بارکردنەکە سەرنەکەووت","Upload in progress":"بارکردنەکە لە جێبەجێکردن دایە",White:"سپی","Widget toolbar":"تووڵامرازی ویدجێت","Wrap text":"",Yellow:"زەرد"}),t.getPluralForm=function(e){return 1!=e}}(window.CKEDITOR_TRANSLATIONS||(window.CKEDITOR_TRANSLATIONS={}));