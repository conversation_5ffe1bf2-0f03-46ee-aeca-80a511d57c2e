image: docker:latest

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  APP_VERSION: "0.0.1"

services:
  - docker:20.10.7-dind

stages:
  - image
  # - transfer
  - deploy

build image develop:
#  image: node:14.18-alpine
  stage: image
  only:
    - develop
  script:
    # nthoang: No test integration for now
    - docker build --no-cache=true --pull -f ./Dockerfile.dev -t ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA} .
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA}

build image qas:
#  image: node:14.18-alpine
  stage: image
  only:
    - qas
  script:
    # nthoang: No test integration for now
    - docker build --no-cache=true --pull -f ./Dockerfile.qas -t ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA} .
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA}

build image prd:
#  image: node:14.18-alpine
  stage: image
  only:
    - master
  script:
    # nthoang: No test integration for now
    - docker build --no-cache=true --pull -f ./Dockerfile.prd -t ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA} .
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA}

# transfer_image:
#  stage: transfer
#  only:
#     - nthoang-cicd-setup
#  script:
#     - docker login -u gitlab-ci-token -p $CI_BUILD_TOKEN $CI_REGISTRY
#     - docker push $CI_REGISTRY_IMAGE:$APP_VERSION

# deploy image:
#   stage: deploy
#   only:
#     - nthoang-cicd-setup
#   variables:
#     DOCKER_HOST: ssh://$SSH_USER@$DESTINATION_HOST
#   before_script:
#     - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
#     - eval $(ssh-agent -s)
#     - mkdir -p ~/.ssh
#     - chmod 700 ~/.ssh
#     - touch ~/.ssh/id_ed25519    # Generate a dummy ed25519 key file
#     - chmod 700 ~/.ssh/id_ed25519
#     - echo "$SSH_PRIVATE_KEY" | base64 -d | tr -d '\r' > ~/.ssh/id_ed25519 | ssh-add ~/.ssh/id_ed25519 > /dev/null
#     - ssh-keyscan -t ed25519 $DESTINATION_HOST #>> ~/.ssh/known_hosts
#     #- cat ~/.ssh/known_hosts
#   script:
#     - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN $CI_REGISTRY
#     - docker pull $CI_REGISTRY_IMAGE:$APP_VERSION
#     - docker-compose -f $DOCKER_COMPOSE_FILE up

update images tag and deploy develop:
  stage: deploy
  image: alpine
  only:
    - develop
  before_script:
    - apk add openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    
  script:
    # - sed -i "s/IMG_TAG/$CI_COMMIT_SHORT_SHA/g" src/main/docker/docker-compose.yml
    # - scp -o StrictHostKeyChecking=no -P 2202 src/main/docker/docker-compose.yml $SSH_USER@$DESTINATION_HOST:/home/<USER>/SalePortal
    - ssh -o StrictHostKeyChecking=no $SSH_USER@$DESTINATION_HOST -p 2202 "docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY; cd /home/<USER>/SalePortal_WebApp; docker pull ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA}; docker stop saleportal_webapp_dev; docker rm saleportal_webapp_dev; docker run --restart=always --name saleportal_webapp_dev -p 3000:4000 -d ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA}; ./wait-for-it.sh localhost:3000 --strict -- echo "Saleportal WebApp is running""

update images tag and deploy qas:
  stage: deploy
  image: alpine
  only:
    - qas
  before_script:
    - apk add openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    
  script:
    # - sed -i "s/IMG_TAG/$CI_COMMIT_SHORT_SHA/g" src/main/docker/docker-compose.yml
    # - scp -o StrictHostKeyChecking=no -P 2202 src/main/docker/docker-compose.yml $SSH_USER@$DESTINATION_HOST:/home/<USER>/SalePortal
    - ssh -o StrictHostKeyChecking=no $SSH_USER@$DESTINATION_HOST -p 2202 "docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY; cd /home/<USER>/SalePortal_WebApp; docker pull ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA}; docker stop saleportal_webapp_qas; docker rm saleportal_webapp_qas; docker run --restart=always --name saleportal_webapp_qas -p 3001:4000 -d ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA}; ./wait-for-it.sh localhost:3001 --strict -- echo "Saleportal WebApp is running""
    

update images tag and deploy prod:
  stage: deploy
  image: alpine
  only:
    - master
  before_script:
    - apk add openssh-client
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY_PRD" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    
  script:
    # - sed -i "s/IMG_TAG/$CI_COMMIT_SHORT_SHA/g" src/main/docker/docker-compose.yml
    # - scp -o StrictHostKeyChecking=no -P 2202 src/main/docker/docker-compose.yml $SSH_USER@$DESTINATION_HOST:/home/<USER>/SalePortal
    - ssh -o StrictHostKeyChecking=no $SSH_USER_PRD@$DESTINATION_HOST_PRD -p 2203 "docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY; cd /home/<USER>/SalePortal_WebApp; docker pull ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA}; docker stop saleportal_webapp_prd; docker rm saleportal_webapp_prd; docker run --name saleportal_webapp_prd -p 3000:4000 -d ${CI_REGISTRY_IMAGE}:${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA}; ./wait-for-it.sh localhost:3000 --strict -- echo "Saleportal WebApp is running""
    
