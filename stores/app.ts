import { defineStore } from 'pinia'

type Error = {
  code?: number
  message?: string
}

export const useAppStore = defineStore('app', {
  state: () => ({
    error: null as Error | null,
    isLoading: false,
    isRequesting: false as boolean,
    isGetInfo: false as boolean,
    userInfo: {} as any,
    listImage: [] as any,
    listMaterial: [] as any,
    listMaterialSearch: [] as any,
    listCustomer: [] as any,
    listCart: useLocalStorage('listCart', [] as any),
    listOrder: useLocalStorage('listOrder', [] as any),
  }),
  getters: {},
  actions: {
    newError(payload: Error) {
      this.error = payload
    },
    startLoading() {
      this.isLoading = true
    },
    finishLoading() {
      this.isLoading = false
    },
    updateRequesting(payload: boolean) {
      this.isRequesting = payload
    },
    increaseQuantity(payload: any) {
      const listCart = this.listCart
      const index = listCart.findIndex(
        (item: any) => item.Salesmatnumber === payload.Salesmatnumber && item.Materialnumber === payload.Materialnumber,
      )
      if (index > -1) {
        listCart[index].quantity++
      } else {
        listCart.push(payload)
      }
    },
    decreaseQuantity(payload: any) {
      const listCart = this.listCart
      const index = listCart.findIndex(
        (item: any) => item.Salesmatnumber === payload.Salesmatnumber && item.Materialnumber === payload.Materialnumber,
      )
      if (index > -1) {
        listCart[index].quantity--
        if (listCart[index].quantity === 0) {
          listCart.splice(index, 1)
        }
      }
    },
    removeItem(payload: any) {
      const listCart = this.listCart
      const index = listCart.findIndex(
        (item: any) => item.Salesmatnumber === payload.Salesmatnumber && item.Materialnumber === payload.Materialnumber,
      )
      if (index > -1) {
        listCart.splice(index, 1)
      }
    },
    addAllCart(payload: any) {
      const listCart = this.listCart
      const index = listCart.findIndex(
        (item: any) => item.Salesmatnumber === payload.Salesmatnumber && item.Materialnumber === payload.Materialnumber,
      )
      if (index > -1) {
        listCart[index].quantity += payload.quantity
      } else {
        listCart.push(payload)
      }
      console.log(payload, listCart)
    },
  },
})
