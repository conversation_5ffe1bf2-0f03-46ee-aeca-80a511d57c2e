const behaviors = [
  ['disable', { 'pointer-events': 'none', opacity: 0.5 }],
  ['clickable', { cursor: 'pointer' }],
  [
    'button',
    {
      border: '1px solid #004C82',
      'border-radius': '4px',
      cursor: 'pointer',
      padding: '12px 24px',
    },
  ],
  [
    'button-primary',
    {
      border: '1px solid #004C82',
      'background-color': '#004C82',
      color: '#fff',
      'border-radius': '4px',
      cursor: 'pointer',
      padding: '12px 24px',
    },
  ],
  [
    'shadow',
    {
      'box-shadow': '0px 1px 12px 0px rgba(0, 0, 0, 0.08)',
    },
  ],
  ['input', { height: '38px ', padding: '8px 16px' }],
  ['select', { height: '30px', 'min-width': '60px' }],
  ['page', { padding: '16px 24px' }],
  [
    'page-heading',
    { 'font-size': '24px', 'line-height': '32px', 'font-weight': 'bold', margin: '0', color: '#1C2E43' },
  ],
  ['com-heading', { 'font-size': '18px', 'line-height': '24px', 'font-weight': 'bold', margin: '0', color: '#1C2E43' }],
]

/*https://material.io/resources/color*/
const colors = [
  [/^c:(.*?)$/, ([, c]) => ({ color: c })],
  [/^bg:(.*?)$/, ([, bg]) => ({ 'background-color': bg })],

  ['c-primary', { color: '#004C82' }],
  ['c-yellow', { color: '#004C82' }],
  ['c-primary-20', { color: '#F2F7F9' }],
  ['c-primary-10', { color: '#F2F7F9' }],
  ['c-primary-15', { color: '#1A78A1' }],
  ['c-danger', { color: '#FF1919' }],
  ['c-red', { color: '#FF1919' }],
  ['c-green-f2', { color: '#F2FFF5' }],
  ['c-green-43', { color: '#43A856' }],
  ['c-black-40', { color: '#E1E1E1' }],
  ['c-black-50', { color: '#C2C2C2' }],
  ['c-black-60', { color: '#8A8A8A' }],
  ['c-black-80', { color: '#484848' }],
  ['c-black-90', { color: '#1C2E43' }],
  ['c-black', { color: '#1C2E43' }],
  ['c-title', { color: '#75A0B9' }],
  ['c-text', { color: '#1C2E43' }],
  ['c-gray-75', { color: '#343A40' }],
  ['c-white', { color: '#ffffff' }],
  ['c-border', { color: '#EFEFEF' }],
  ['c-r-error', { color: '#FF4A4A' }],
  ['c-gr-text', { color: '#2AC86B' }],
  ['c-ye-dark', { color: '#F6A609' }],
  ['c-red-11', { color: '#FC4E4F' }],
  ['c-secondary-80', { color: '#FFCA60' }],
  ['bg-danger', { 'background-color': '#FFF0F0' }],
  ['bg-black-10', { 'background-color': '#F4F4F4' }],
  ['bg-black-20', { 'background-color': '#EFEFEF' }],
  ['bg-black-30', { 'background-color': '#E9E9E9' }],
  ['bg-black-40', { 'background-color': '#E1E1E1' }],
  ['bg-black-50', { 'background-color': '#C2C2C2' }],
  ['bg-primary', { 'background-color': '#004C82' }],
  ['bg-primary-10', { 'background-color': '#F2F7F9' }],
  ['bg-primary-20', { 'background-color': '#E0EFFF' }],
  ['bg-green-f2', { 'background-color': '#F2FFF5' }],
  ['bg-green-43', { 'background-color': '#43A856' }],
  ['bg-yellow-light', { 'background-color': '#FFF2D8' }],
  ['bg-green-light', { 'background-color': '#E7FEE9' }],
  ['bg-secondary-5', { 'background-color': '#FFFAF0' }],
  ['bg-green-tint', { 'background-color': '#48D58C' }],
  ['bg-tint-shamrock-10', { 'background-color': '#E8FAF1' }],
  ['bg-yellow', { 'background-color': '#FFCA60' }],
  ['bg-error', { 'background-color': '#FF4A4A' }],
  ['bg-red', { 'background-color': '#FF1919' }],
  ['bg-black', { 'background-color': '#000' }],
  ['bg-white', { 'background-color': '#fff' }],

  ['image-white', { filter: 'brightness(0) invert(1)' }],
]

const layouts = [
  /* sizing */
  [/^h-(\d+)px/, ([, d]) => ({ height: `${d}px` })],
  [/^w-(\d+)px/, ([, d]) => ({ width: `${d}px` })],
  [/^w-(\d+)%/, ([, d]) => ({ width: `${d}%` })],

  ['icon', { width: '24px', height: '24px' }],
  [/^icon-(\d+)px/, ([, d]) => ({ height: `${d}px`, width: `${d}px` })],

  ['h-50', { height: '100%' }],
  ['h-100', { height: '100%' }],
  ['h-100vh', { height: '100vh' }],
  ['w-50', { width: '50%' }],
  ['w-100', { width: '100%' }],
  ['w-100vw', { width: '100vw' }],
  ['w-fit', { width: 'fit-content' }],
  ['w-max', { width: 'max-content' }],

  /* overflow */
  ['ovf-h', { overflow: 'hidden' }],
  ['ovf-a', { overflow: 'auto' }],
  ['ovf-x-s', { 'overflow-x': 'scroll' }],
  ['ovf-x-h', { 'overflow-x': 'hidden' }],
  ['ovf-x-a', { 'overflow-x': 'auto' }],
  ['ovf-y-s', { 'overflow-y': 'scroll' }],
  ['ovf-y-h', { 'overflow-y': 'hidden' }],
  ['ovf-y-a', { 'overflow-y': 'auto' }],
  [
    'hide-scroll-bar',
    {
      '-ms-overflow-style': 'none',
      /* IE, Edge */ 'scrollbar-width': 'none' /* Firefox */,
    },
  ],
  ['hide-scroll-bar::-webkit-scrollbar', { display: 'none' }] /* Chrome, Safari and Opera */,

  /* flex */
  ['fr', { display: 'flex', 'flex-direction': 'row' }],
  ['fc', { display: 'flex', 'flex-direction': 'column' }],
  ['fw-w', { 'flex-wrap': 'wrap' }],
  [/^fg-(\d+)$/, ([, d]) => ({ gap: `${d / 4}rem` })],
  [/^f(\d+)$/, ([, d]) => ({ flex: d })],
  ['ai-bl', { 'align-items': 'baseline' }],
  ['ai-c', { 'align-items': 'center' }],
  ['ai-e', { 'align-items': 'flex-end' }],
  ['ai-s', { 'align-items': 'flex-start' }],
  ['jc-c', { 'justify-content': 'center' }],
  ['jc-sa', { 'justify-content': 'space-around' }],
  ['jc-sb', { 'justify-content': 'space-between' }],
  ['jc-se', { 'justify-content': 'space-evenly' }],
  ['jc-fe', { 'justify-content': 'flex-end' }],
  ['jc-fs', { 'justify-content': 'flex-start' }],
  ['jc-n', { 'justify-content': 'normal' }],
  ['jc-r', { 'justify-content': 'revert' }],

  /* grid */
  ['grid', { display: 'grid' }],
  [/^gtc-(\d+)-(\d+)$/, ([, d1, d2]) => ({ 'grid-template-columns': `${d1} ${d2}` })],
  [/^gtc-(\d+)-(\d+)-(\d+)$/, ([, d1, d2, d3]) => ({ 'grid-template-columns': `${d1} ${d2} ${d3}` })],
  [
    /^gtc-(\d+)-(\d+)-(\d+)-(\d+)$/,
    ([, d1, d2, d3, d4]) => ({
      'grid-template-columns': `${d1} ${d2} ${d3} ${d4}`,
    }),
  ],
  [
    /^gtc-(\d+)-(\d+)-(\d+)-(\d+)-(\d+)$/,
    ([, d1, d2, d3, d4, d5]) => ({
      'grid-template-columns': `${d1} ${d2} ${d3} ${d4} ${d5}`,
    }),
  ],
  [
    /^gtc-(\d+)-(\d+)-(\d+)-(\d+)-(\d+)-(\d+)$/,
    ([, d1, d2, d3, d4, d5, d6]) => ({
      'grid-template-columns': `${d1} ${d2} ${d3} ${d4} ${d5} ${d6}`,
    }),
  ],
  [/^gtr-(\d+)-(\d+)$/, ([, d1, d2]) => ({ 'grid-template-rows': `${d1} ${d2}` })],
  [/^gtr-(\d+)-(\d+)-(\d+)$/, ([, d1, d2, d3]) => ({ 'grid-template-rows': `${d1} ${d2} ${d3}` })],
  [
    /^gtr-(\d+)-(\d+)-(\d+)-(\d+)$/,
    ([, d1, d2, d3, d4]) => ({
      'grid-template-rows': `${d1} ${d2} ${d3} ${d4}`,
    }),
  ],
  [
    /^gtr-(\d+)-(\d+)-(\d+)-(\d+)-(\d+)$/,
    ([, d1, d2, d3, d4, d5]) => ({
      'grid-template-rows': `${d1} ${d2} ${d3} ${d4} ${d5}`,
    }),
  ],
  [
    /^gtr-(\d+)-(\d+)-(\d+)-(\d+)-(\d+)-(\d+)$/,
    ([, d1, d2, d3, d4, d5, d6]) => ({
      'grid-template-rows': `${d1} ${d2} ${d3} ${d4} ${d5} ${d6}`,
    }),
  ],
]

const borderColor = [
  ['border-black-10', { 'border-color': '#F4F4F4' }],
  ['border-black-20', { 'border-color': '#EFEFEF' }],
  ['border-black-30', { 'border-color': '#E9E9E9' }],
  ['border-black-40', { 'border-color': '#E1E1E1' }],
  ['border-black-50', { 'border-color': '#C2C2C2' }],
  ['border-gray-10', { 'border-color': '#F6F6F7' }],
  ['border-gray-20', { 'border-color': '#EEEEF0' }],
  ['border-primary', { 'border-color': '#004C82' }],
  ['border-secondary-main', { 'border-color': '#1E394F' }],
  ['border-b-color', { 'border-bottom-color': '#EFEFEF' }],
  ['border-green-f2', { 'border-color': '#F2FFF5' }],
  ['border-green-43', { 'border-color': '#43A856' }],
]

const borderRadius = [
  [/^br-(\d+)px$/, ([, d]) => ({ 'border-radius': `${d}px` })],
  [/^br-(\d+)$/, ([, d]) => ({ 'border-radius': `${d / 4}rem` })],
  [/^br-(\d+)pt$/, ([, pt]) => ({ 'border-radius': `${pt}%` })],
  // ['rounded', { 'border-radius': '8px' }],
]

const position = [
  ['abs', { position: 'absolute' }],
  ['fix', { position: 'fixed' }],
  ['rel', { position: 'relative' }],
  ['sta', { position: 'static' }],
  ['sti', { position: 'sticky' }],
  [/^top-(\d+)/, ([, d]) => ({ top: `${d}px` })],
  [/^left-(\d+)/, ([, d]) => ({ left: `${d}px` })],
  [/^bottom-(\d+)/, ([, d]) => ({ bottom: `${d}px` })],
  [/^right-(\d+)/, ([, d]) => ({ right: `${d}px` })],
]

const spacing = [
  [/^p-(\d+)$/, ([, d]) => ({ padding: `${d / 4}rem` })],
  [/^pt-(\d+)$/, ([, d]) => ({ 'padding-top': `${d / 4}rem` })],
  [/^pl-(\d+)$/, ([, d]) => ({ 'padding-left': `${d / 4}rem` })],
  [/^pb-(\d+)$/, ([, d]) => ({ 'padding-bottom': `${d / 4}rem` })],
  [/^pr-(\d+)$/, ([, d]) => ({ 'padding-right': `${d / 4}rem` })],
  [
    /^px-(\d+)$/,
    ([, d]) => ({
      'padding-left': `${d / 4}rem`,
      'padding-right': `${d / 4}rem`,
    }),
  ],
  [
    /^py-(\d+)$/,
    ([, d]) => ({
      'padding-top': `${d / 4}rem`,
      'padding-bottom': `${d / 4}rem`,
    }),
  ],

  [/^m-(\d+)$/, ([, d]) => ({ margin: `${d / 4}rem` })],
  [/^mt-(\d+)$/, ([, d]) => ({ 'margin-top': `${d / 4}rem` })],
  [/^ml-(\d+)$/, ([, d]) => ({ 'margin-left': `${d / 4}rem` })],
  [/^mb-(\d+)$/, ([, d]) => ({ 'margin-bottom': `${d / 4}rem` })],
  [/^mr-(\d+)$/, ([, d]) => ({ 'margin-right': `${d / 4}rem` })],
  [/^mx-(\d+)$/, ([, d]) => ({ 'margin-left': `${d / 4}rem`, 'margin-right': `${d / 4}rem` })],
  [/^my-(\d+)$/, ([, d]) => ({ 'margin-top': `${d / 4}rem`, 'margin-bottom': `${d / 4}rem` })],
  ['mx-a', { margin: '0 auto' }],
]

const text = [
  ['t-t--u', { 'text-transform': 'uppercase' }],
  ['t-t--c', { 'text-transform': 'capitalize' }],
  ['t-t--l', { 'text-transform': 'lowercase' }],
  ['ta-l', { 'text-align': 'left' }],
  ['ta-r', { 'text-align': 'right' }],
  ['ta-c', { 'text-align': 'center' }],
  [/^fw-(\d)/, ([_, d]) => ({ 'font-weight': `${d}00` })],
  ['fs-xs', { 'font-size': '0.75rem' }],
  ['fs-s', { 'font-size': '0.875rem' }],
  ['fs-m', { 'font-size': '1rem' }],
  ['fs-l', { 'font-size': '1.15em' }],
  ['text-xs', { 'font-size': '0.75rem', 'line-height': '1rem' }],
  ['text-s', { 'font-size': '0.875rem', 'line-height': '1.25rem' }],
  ['text-m', { 'font-size': '1rem', 'line-height': '1.5rem' }],
  ['text-l', { 'font-size': '1.25rem', 'line-height': '1.875rem' }],

  [/^fs-(\d+)px$/, ([, d]) => ({ 'font-size': `${d}px` })],
  [/^fs-(\d+)rem$/, ([, d]) => ({ 'font-size': `${d}rem` })],
  [/^fs-(\d+)rem$/, ([, d]) => ({ 'font-size': `${d}rem` })],
]

const layering = [[/^z-index-(\d+)$/, ([, d]) => ({ 'z-index': d })]]

export default [
  ...behaviors,
  ...colors,
  ...layouts,
  ...borderColor,
  ...borderRadius,
  ...position,
  ...spacing,
  ...text,
  ...layering,
]
