export const listPermissions = [
  // { key: 'dashboard', label: 'Dashboard', children: [] },
  {
    key: 'application',
    label: 'Application',
  },
  {
    key: 'agents',
    label: 'Agent',
    children: [
      { key: 'view_agent', label: 'View' },
      { key: 'edit_agent', label: 'Edit' },
      { key: 'create_agent', label: 'Create' },
      { key: 'delete_agent', label: 'Delete' },
      { key: 'login_agent', label: 'Login Agent' },
    ],
  },
  {
    key: 'checklists',
    label: 'Checklist',
    children: [
      { key: 'view_checklist', label: 'View' },
      { key: 'edit_checklist', label: 'Edit' },
      { key: 'create_checklist', label: 'Create' },
      { key: 'delete_checklist', label: 'Delete' },
    ],
  },

  {
    key: 'admins',
    label: 'Users Admin',
    children: [
      { key: 'view_admin', label: 'View' },
      { key: 'edit_admin', label: 'Edit' },
      { key: 'create_admin', label: 'Create' },
      { key: 'delete_admin', label: 'Delete' },
      {
        key: 'roles',
        label: 'Roles',
        children: [
          { key: 'view_role', label: 'View' },
          { key: 'edit_role', label: 'Edit' },
          { key: 'create_role', label: 'Create' },
          { key: 'delete_role', label: 'Delete' },
        ],
      },
    ],
  },

  { key: 'payment', label: 'Payment', children: [] },
  { key: 'contact', label: 'Contact', children: [] },
  // { key: 'login_agent', label: 'Login Agent', children: [] },

  // {
  //   key: 'resources',
  //   label: 'Resources',
  //   children: [
  //     { key: 'view_resource', label: 'View' },
  //     { key: 'edit_resource', label: 'Edit' },
  //     { key: 'create_resource', label: 'Create' },
  //     { key: 'delete_resource', label: 'Delete' },
  //   ],
  // },
]

export const listPermissionsAgent = [
  {
    key: 'applications',
    label: 'Application',
    children: [
      { key: 'manager_application', label: 'Manager Application' },
      { key: 'payment_application', label: 'Payment Application' },
      { key: 'create_application', label: 'Create' },
      { key: 'external_application', label: 'External' },
      { key: 'payment_application', label: 'Payment Application' },
    ],
  },
  {
    key: 'checklists',
    label: 'Check Lists',
    children: [
      { key: 'view_checklist', label: 'View' },
      { key: 'edit_checklist', label: 'Edit' },
      { key: 'create_checklist', label: 'Create' },
      { key: 'delete_checklist', label: 'Delete' },
    ],
  },
  {
    key: 'connection_agent',
    label: 'Connection Agent',
    children: [],
  },
  {
    key: 'connection_school',
    label: 'Connection School',
    children: [],
  },
  {
    key: 'accounts',
    label: 'Employees',
    children: [
      { key: 'view_account', label: 'View' },
      { key: 'edit_account', label: 'Edit' },
      { key: 'create_account', label: 'Create' },
      { key: 'delete_account', label: 'Delete' },
      {
        key: 'roles',
        label: 'Roles',
        children: [
          { key: 'view_role', label: 'View' },
          { key: 'edit_role', label: 'Edit' },
          { key: 'create_role', label: 'Create' },
          { key: 'delete_role', label: 'Delete' },
        ],
      },
    ],
  },
  {
    key: 'payment',
    label: 'Payment',
  },
]
